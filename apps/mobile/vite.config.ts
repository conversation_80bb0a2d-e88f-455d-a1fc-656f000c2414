import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
    },
  },
  server: {
    port: 3001,
    host: '0.0.0.0',
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          ionic: ['@ionic/react', '@ionic/react-router'],
          capacitor: ['@capacitor/core', '@capacitor/app'],
        },
      },
    },
  },
  define: {
    'process.env': {},
  },
  optimizeDeps: {
    include: [
      '@ionic/react',
      '@ionic/react-router',
      '@capacitor/core',
      '@capacitor/app',
      'react',
      'react-dom',
    ],
  },
});