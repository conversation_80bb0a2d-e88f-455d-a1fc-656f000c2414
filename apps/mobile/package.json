{"name": "mobile", "version": "0.1.0", "private": true, "scripts": {"build": "vite build", "dev": "vite", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "typecheck": "tsc --noEmit", "test": "jest", "clean": "rm -rf dist build", "ionic:build": "ionic build", "ionic:serve": "ionic serve"}, "dependencies": {"@foodprepai/shared-types": "*", "@foodprepai/shared-utils": "*", "@foodprepai/api-client": "*", "@foodprepai/ui-components": "*", "@capacitor-community/barcode-scanner": "^4.0.1", "@capacitor/android": "^6.0.0", "@capacitor/app": "^6.0.0", "@capacitor/core": "^6.0.0", "@capacitor/filesystem": "^6.0.0", "@capacitor/haptics": "^6.0.0", "@capacitor/ios": "^6.0.0", "@capacitor/keyboard": "^6.0.0", "@capacitor/share": "^6.0.0", "@capacitor/status-bar": "^6.0.0", "@ionic/react": "^8.0.0", "@ionic/react-router": "^8.0.0", "@ionic/storage": "^4.0.0", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-form": "^0.1.1", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@tailwindcss/typography": "^0.5.15", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^2.30.0", "dexie": "^4.0.10", "dexie-react-hooks": "^1.1.7", "ionicons": "^7.0.0", "lucide-react": "^0.469.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router": "^6.8.1", "react-router-dom": "^6.8.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@foodprepai/eslint-config": "*", "@foodprepai/tsconfig": "*", "@capacitor/cli": "^6.0.0", "@ionic/cli": "^7.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/react-router": "^5.1.20", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.0.0", "eslint": "^8.57.0", "typescript": "^5.0.0", "vite": "^5.0.0"}}