// Common API response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  totalCount: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

// Request types
export interface PaginationParams {
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface SearchParams extends PaginationParams {
  query?: string;
  filters?: Record<string, any>;
}

// Auth types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface PinLoginRequest {
  pin: string;
  companyId: string;
  locationId: string;
}

export interface LoginResponse {
  token: string;
  refreshToken: string;
  user: User;
  expiresIn: number;
}

export interface PinLoginResponse {
  success: boolean;
  token: string;
  user: POSUser;
  message?: string;
}

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'admin' | 'manager' | 'staff';
  permissions: string[];
  restaurantId: string;
}

export interface POSUser {
  id: string;
  name: string;
  pin: string;
  role: 'cashier' | 'manager' | 'admin';
  companyId: string;
  locationId: string;
  isActive: boolean;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

// Company and Location types
export interface Company {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Location {
  id: string;
  name: string;
  address: string;
  phone?: string;
  email?: string;
  companyId: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Branch Inventory types
export interface BranchInventoryItem {
  id: string;
  name: string;
  description?: string;
  category: string;
  price: number;
  cost?: number;
  isAvailable: boolean;
  stockLevel?: number;
  unit?: string;
  imageUrl?: string;
  alternatives?: BranchInventoryAlternative[];
}

export interface BranchInventoryAlternative {
  id: string;
  name: string;
  description?: string;
  price: number;
  isAvailable: boolean;
}

export interface BranchInventoryCategory {
  category: string;
  items: BranchInventoryItem[];
}

export interface BranchInventoryResponse {
  success: boolean;
  data: BranchInventoryCategory[];
  message?: string;
}

// Restaurant types
export interface Restaurant {
  id: string;
  name: string;
  address: string;
  phone: string;
  email: string;
  timezone: string;
  currency: string;
  taxRate: number;
  settings: RestaurantSettings;
}

export interface RestaurantSettings {
  allowTips: boolean;
  defaultTipPercentages: number[];
  requireCustomerInfo: boolean;
  enableInventoryTracking: boolean;
  enableDelivery: boolean;
  deliveryRadius: number;
  deliveryFee: number;
  minimumOrderAmount: number;
}