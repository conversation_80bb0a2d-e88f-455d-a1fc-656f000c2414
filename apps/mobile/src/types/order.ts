import { CartItem } from './menu';

export type OrderStatus = 
  | 'pending' 
  | 'preparing' 
  | 'ready' 
  | 'completed' 
  | 'cancelled';

export type OrderType = 'dine-in' | 'takeout' | 'delivery';

export type PaymentMethod = 'cash' | 'card' | 'digital_wallet' | 'other';

export type PaymentStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'refunded';

export interface OrderItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  modifiers?: Array<{
    name: string;
    price: number;
  }>;
  specialInstructions?: string;
}

export interface Order {
  id: string;
  customerName?: string;
  customerPhone?: string;
  customerEmail?: string;
  items: OrderItem[];
  subtotal: number;
  tax: number;
  tip?: number;
  discount?: number;
  total: number;
  status: OrderStatus;
  orderType: OrderType;
  orderTime: Date;
  estimatedCompletion?: Date;
  completedTime?: Date;
  tableNumber?: number;
  deliveryAddress?: string;
  specialInstructions?: string;
  paymentMethod?: PaymentMethod;
  paymentStatus?: PaymentStatus;
  paymentTransactionId?: string;
  assignedStaff?: string;
}

export interface OrderSummary {
  totalOrders: number;
  pendingOrders: number;
  preparingOrders: number;
  readyOrders: number;
  completedOrders: number;
  totalRevenue: number;
  averageOrderValue: number;
  averagePreparationTime: number;
}

export type OrderFilter = {
  status?: OrderStatus[];
  orderType?: OrderType[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  customerName?: string;
};

// POS-specific types
export interface POSCartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  stockLevel?: number;
  category: string;
  imageUrl?: string;
  description?: string;
  isAvailable: boolean;
  modifiers?: Array<{
    id: string;
    name: string;
    price: number;
  }>;
  specialInstructions?: string;
  timestamp: number; // for unique identification in cart
}

export interface POSTransaction {
  id: string;
  receiptNumber: string;
  items: OrderItem[];
  subtotal: number;
  tax: number;
  tip?: number;
  discount?: number;
  total: number;
  paymentMethod: PaymentMethod;
  paymentStatus: PaymentStatus;
  timestamp: string;
  customerName?: string;
  customerPhone?: string;
  customerEmail?: string;
  tableNumber?: number;
  specialInstructions?: string;
  cashierName?: string;
  cashierId?: string;
  companyId: string;
  locationId: string;
  isRefunded?: boolean;
  refundAmount?: number;
  refundReason?: string;
}

export interface POSPayment {
  method: PaymentMethod;
  amount: number;
  amountPaid?: number; // for cash payments
  change?: number; // for cash payments
  cardLast4?: string; // for card payments
  cardType?: string; // for card payments
  transactionId?: string;
  timestamp: string;
}

export interface POSReceipt {
  transaction: POSTransaction;
  payment: POSPayment;
  companyInfo: {
    name: string;
    address: string;
    phone: string;
    email?: string;
  };
  locationInfo: {
    name: string;
    address: string;
  };
  printedAt: string;
  reprintCount?: number;
}