import { apiService } from './api';
import { 
  MenuItem, 
  MenuCategory, 
  ApiResponse, 
  PaginatedResponse,
  BranchInventoryResponse,
  BranchInventoryCategory,
  BranchInventoryItem
} from '@/types';

export class MenuService {
  private endpoint = '/menu';
  private posEndpoint = '/pos';

  // Legacy menu methods (kept for backward compatibility)
  async getMenuItems(): Promise<ApiResponse<MenuItem[]>> {
    return apiService.get<MenuItem[]>(`${this.endpoint}/items`);
  }

  async getMenuItem(id: string): Promise<ApiResponse<MenuItem>> {
    return apiService.get<MenuItem>(`${this.endpoint}/items/${id}`);
  }

  async createMenuItem(item: Omit<MenuItem, 'id'>): Promise<ApiResponse<MenuItem>> {
    return apiService.post<MenuItem>(`${this.endpoint}/items`, item);
  }

  async updateMenuItem(id: string, item: Partial<MenuItem>): Promise<ApiResponse<MenuItem>> {
    return apiService.put<MenuItem>(`${this.endpoint}/items/${id}`, item);
  }

  async deleteMenuItem(id: string): Promise<ApiResponse<void>> {
    return apiService.delete<void>(`${this.endpoint}/items/${id}`);
  }

  async getCategories(): Promise<ApiResponse<MenuCategory[]>> {
    return apiService.get<MenuCategory[]>(`${this.endpoint}/categories`);
  }

  async createCategory(category: Omit<MenuCategory, 'id'>): Promise<ApiResponse<MenuCategory>> {
    return apiService.post<MenuCategory>(`${this.endpoint}/categories`, category);
  }

  async updateCategory(id: string, category: Partial<MenuCategory>): Promise<ApiResponse<MenuCategory>> {
    return apiService.put<MenuCategory>(`${this.endpoint}/categories/${id}`, category);
  }

  async deleteCategory(id: string): Promise<ApiResponse<void>> {
    return apiService.delete<void>(`${this.endpoint}/categories/${id}`);
  }

  async getMenuItemsByCategory(categoryId: string): Promise<ApiResponse<MenuItem[]>> {
    return apiService.get<MenuItem[]>(`${this.endpoint}/categories/${categoryId}/items`);
  }

  async toggleItemAvailability(id: string, isAvailable: boolean): Promise<ApiResponse<MenuItem>> {
    return apiService.patch<MenuItem>(`${this.endpoint}/items/${id}/availability`, { isAvailable });
  }

  // New POS-specific methods for FoodPrepAI backend
  
  /**
   * Get branch inventory items (grouped by category)
   * This is the main method for POS systems to get available items
   */
  async getBranchInventory(): Promise<BranchInventoryCategory[]> {
    try {
      const response = await apiService.get<BranchInventoryResponse>(`${this.posEndpoint}/branch-inventories`);
      
      if (response.success && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to fetch branch inventory');
    } catch (error) {
      console.error('Failed to get branch inventory:', error);
      throw error;
    }
  }

  /**
   * Get all available items from branch inventory (flattened)
   */
  async getAllInventoryItems(): Promise<BranchInventoryItem[]> {
    try {
      const categories = await this.getBranchInventory();
      const allItems: BranchInventoryItem[] = [];
      
      categories.forEach(category => {
        allItems.push(...category.items);
      });
      
      return allItems;
    } catch (error) {
      console.error('Failed to get all inventory items:', error);
      throw error;
    }
  }

  /**
   * Get items by category from branch inventory
   */
  async getInventoryItemsByCategory(categoryName: string): Promise<BranchInventoryItem[]> {
    try {
      const categories = await this.getBranchInventory();
      const category = categories.find(cat => 
        cat.category.toLowerCase() === categoryName.toLowerCase()
      );
      
      return category?.items || [];
    } catch (error) {
      console.error('Failed to get inventory items by category:', error);
      throw error;
    }
  }

  /**
   * Search for items by name across all categories
   */
  async searchInventoryItems(query: string): Promise<BranchInventoryItem[]> {
    try {
      const allItems = await this.getAllInventoryItems();
      const searchTerm = query.toLowerCase();
      
      return allItems.filter(item => 
        item.name.toLowerCase().includes(searchTerm) ||
        item.description?.toLowerCase().includes(searchTerm) ||
        item.category.toLowerCase().includes(searchTerm)
      );
    } catch (error) {
      console.error('Failed to search inventory items:', error);
      throw error;
    }
  }

  /**
   * Get only available items from branch inventory
   */
  async getAvailableInventoryItems(): Promise<BranchInventoryItem[]> {
    try {
      const allItems = await this.getAllInventoryItems();
      return allItems.filter(item => item.isAvailable);
    } catch (error) {
      console.error('Failed to get available inventory items:', error);
      throw error;
    }
  }

  /**
   * Get category names from branch inventory
   */
  async getInventoryCategories(): Promise<string[]> {
    try {
      const categories = await this.getBranchInventory();
      return categories.map(category => category.category);
    } catch (error) {
      console.error('Failed to get inventory categories:', error);
      throw error;
    }
  }

  /**
   * Get a specific item by ID from branch inventory
   */
  async getInventoryItem(itemId: string): Promise<BranchInventoryItem | null> {
    try {
      const allItems = await this.getAllInventoryItems();
      return allItems.find(item => item.id === itemId) || null;
    } catch (error) {
      console.error('Failed to get inventory item:', error);
      throw error;
    }
  }

  /**
   * Transform branch inventory data to legacy MenuItem format for backward compatibility
   */
  async getInventoryAsMenuItems(): Promise<MenuItem[]> {
    try {
      const allItems = await this.getAllInventoryItems();
      
      return allItems.map(item => ({
        id: item.id,
        name: item.name,
        description: item.description || '',
        price: item.price,
        category: item.category,
        isAvailable: item.isAvailable,
        image: item.imageUrl,
        // Map additional fields if needed
        ingredients: [], // Default empty array
        allergens: [], // Default empty array
        nutritionalInfo: {}, // Default empty object
        preparationTime: 0, // Default value
        customizations: [] // Default empty array
      }));
    } catch (error) {
      console.error('Failed to transform inventory to menu items:', error);
      throw error;
    }
  }
}

export const menuService = new MenuService();