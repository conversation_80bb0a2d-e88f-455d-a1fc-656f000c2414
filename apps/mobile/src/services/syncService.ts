import { db } from './db';
import { apiService } from './api';

export class SyncService {
  private syncInterval: NodeJS.Timeout | null = null;
  private readonly SYNC_INTERVAL_MS = 30000; // 30 seconds
  private isSyncing = false;
  private currentCompanyId: string | null = null;
  private currentLocationId: string | null = null;

  /**
   * Start periodic sync for a specific company/location
   */
  startSync(companyId: string, locationId: string): void {
    this.currentCompanyId = companyId;
    this.currentLocationId = locationId;

    // Clear any existing sync interval
    this.stopSync();

    // Start new sync interval
    this.syncInterval = setInterval(() => {
      if (navigator.onLine && !this.isSyncing) {
        this.performSync();
      }
    }, this.SYNC_INTERVAL_MS);

    // Perform initial sync
    if (navigator.onLine) {
      this.performSync();
    }
  }

  /**
   * Stop periodic sync
   */
  stopSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
    this.currentCompanyId = null;
    this.currentLocationId = null;
    this.isSyncing = false;
  }

  /**
   * Perform a manual sync
   */
  async performSync(): Promise<void> {
    if (!this.currentCompanyId || !this.currentLocationId || this.isSyncing) {
      return;
    }

    this.isSyncing = true;

    try {
      // Process outgoing sync queue (local changes to server)
      await db.processSyncQueue(this.currentCompanyId, this.currentLocationId);

      // Pull latest data from server
      await this.pullServerData();

    } catch (error) {
      console.error('Sync failed:', error);
    } finally {
      this.isSyncing = false;
    }
  }

  /**
   * Perform initial data sync when user logs in
   */
  async initialSync(companyId: string, locationId: string): Promise<void> {
    try {
      this.isSyncing = true;

      // Clear existing data for this company/location
      await db.clearCompanyData(companyId, locationId);

      // Fetch initial data from server
      const [products, categories, inventory, users] = await Promise.all([
        this.fetchProducts(companyId, locationId),
        this.fetchCategories(companyId, locationId),
        this.fetchInventory(companyId, locationId),
        this.fetchUsers(companyId, locationId)
      ]);

      // Bulk import data
      await db.bulkImport({
        products,
        categories,
        inventory,
        users
      }, companyId, locationId);

      console.log('Initial sync completed successfully');

    } catch (error) {
      console.error('Initial sync failed:', error);
      throw error;
    } finally {
      this.isSyncing = false;
    }
  }

  /**
   * Pull latest data from server
   */
  private async pullServerData(): Promise<void> {
    if (!this.currentCompanyId || !this.currentLocationId) {
      return;
    }

    try {
      // Get last sync timestamps
      const lastProductSync = await db.getAppState('lastProductSync', this.currentCompanyId, this.currentLocationId);
      const lastInventorySync = await db.getAppState('lastInventorySync', this.currentCompanyId, this.currentLocationId);
      const lastUserSync = await db.getAppState('lastUserSync', this.currentCompanyId, this.currentLocationId);

      // Fetch updated data
      const promises = [];

      if (!lastProductSync || this.shouldRefresh(lastProductSync)) {
        promises.push(this.syncProducts());
      }

      if (!lastInventorySync || this.shouldRefresh(lastInventorySync)) {
        promises.push(this.syncInventory());
      }

      if (!lastUserSync || this.shouldRefresh(lastUserSync)) {
        promises.push(this.syncUsers());
      }

      await Promise.all(promises);

    } catch (error) {
      console.error('Pull server data failed:', error);
    }
  }

  /**
   * Check if data should be refreshed (older than 5 minutes)
   */
  private shouldRefresh(lastSync: string): boolean {
    const lastSyncTime = new Date(lastSync);
    const now = new Date();
    const timeDiff = now.getTime() - lastSyncTime.getTime();
    return timeDiff > 5 * 60 * 1000; // 5 minutes
  }

  /**
   * Sync products and categories
   */
  private async syncProducts(): Promise<void> {
    try {
      const response = await apiService.get(`/pos/branch-inventory`);
      
      if (response.success && response.data) {
        const categories = response.data;
        const products = [];
        const categoryList = [];

        for (const categoryData of categories) {
          categoryList.push({
            _id: `category_${categoryData.category}`,
            name: categoryData.category,
            description: '',
            companyId: this.currentCompanyId!,
            locationId: this.currentLocationId!,
            syncStatus: 'SYNCED' as const,
            lastSyncAt: new Date()
          });

          for (const item of categoryData.items) {
            products.push({
              _id: item.id,
              name: item.name,
              description: item.description,
              category: categoryData.category,
              price: item.price,
              cost: item.cost,
              isAvailable: item.isAvailable,
              stockLevel: item.stockLevel,
              unit: item.unit,
              imageUrl: item.imageUrl,
              alternatives: item.alternatives || [],
              companyId: this.currentCompanyId!,
              locationId: this.currentLocationId!,
              syncStatus: 'SYNCED' as const,
              lastSyncAt: new Date()
            });
          }
        }

        // Update local database
        await db.transaction('rw', [db.categories, db.products], async () => {
          await db.categories.where('companyId').equals(this.currentCompanyId!).delete();
          await db.products.where('companyId').equals(this.currentCompanyId!).delete();
          
          await db.categories.bulkAdd(categoryList);
          await db.products.bulkAdd(products);
        });

        // Update sync timestamp
        await db.setAppState('lastProductSync', new Date().toISOString(), this.currentCompanyId, this.currentLocationId);
      }
    } catch (error) {
      console.error('Failed to sync products:', error);
    }
  }

  /**
   * Sync inventory data
   */
  private async syncInventory(): Promise<void> {
    try {
      const response = await apiService.get(`/pos/inventory`);
      
      if (response.success && response.data) {
        const inventoryItems = response.data.map((item: any) => ({
          _id: item.id,
          name: item.name,
          category: item.category,
          currentStock: item.currentStock,
          minStock: item.minStock,
          maxStock: item.maxStock,
          unit: item.unit,
          costPerUnit: item.costPerUnit,
          supplier: item.supplier,
          supplierContact: item.supplierContact,
          sku: item.sku,
          barcode: item.barcode,
          location: item.location,
          lastRestocked: item.lastRestocked ? new Date(item.lastRestocked) : undefined,
          expiryDate: item.expiryDate ? new Date(item.expiryDate) : undefined,
          notes: item.notes,
          isActive: item.isActive,
          companyId: this.currentCompanyId!,
          locationId: this.currentLocationId!,
          syncStatus: 'SYNCED' as const,
          lastSyncAt: new Date()
        }));

        // Update local database
        await db.transaction('rw', [db.inventory], async () => {
          await db.inventory.where('companyId').equals(this.currentCompanyId!).delete();
          await db.inventory.bulkAdd(inventoryItems);
        });

        // Update sync timestamp
        await db.setAppState('lastInventorySync', new Date().toISOString(), this.currentCompanyId, this.currentLocationId);
      }
    } catch (error) {
      console.error('Failed to sync inventory:', error);
    }
  }

  /**
   * Sync users data
   */
  private async syncUsers(): Promise<void> {
    try {
      const response = await apiService.get(`/pos/users`);
      
      if (response.success && response.data) {
        const users = response.data.map((user: any) => ({
          _id: user.id,
          name: user.name,
          pin: user.pin,
          role: user.role,
          companyId: this.currentCompanyId!,
          locationId: this.currentLocationId!,
          isActive: user.isActive,
          syncStatus: 'SYNCED' as const,
          lastSyncAt: new Date()
        }));

        // Update local database
        await db.transaction('rw', [db.users], async () => {
          await db.users.where('companyId').equals(this.currentCompanyId!).delete();
          await db.users.bulkAdd(users);
        });

        // Update sync timestamp
        await db.setAppState('lastUserSync', new Date().toISOString(), this.currentCompanyId, this.currentLocationId);
      }
    } catch (error) {
      console.error('Failed to sync users:', error);
    }
  }

  /**
   * Fetch products from server
   */
  private async fetchProducts(companyId: string, locationId: string): Promise<any[]> {
    try {
      const response = await apiService.get(`/pos/branch-inventory`);
      
      if (response.success && response.data) {
        const products = [];
        
        for (const categoryData of response.data) {
          for (const item of categoryData.items) {
            products.push({
              _id: item.id,
              name: item.name,
              description: item.description,
              category: categoryData.category,
              price: item.price,
              cost: item.cost,
              isAvailable: item.isAvailable,
              stockLevel: item.stockLevel,
              unit: item.unit,
              imageUrl: item.imageUrl,
              alternatives: item.alternatives || []
            });
          }
        }
        
        return products;
      }
      
      return [];
    } catch (error) {
      console.error('Failed to fetch products:', error);
      return [];
    }
  }

  /**
   * Fetch categories from server
   */
  private async fetchCategories(companyId: string, locationId: string): Promise<any[]> {
    try {
      const response = await apiService.get(`/pos/branch-inventory`);
      
      if (response.success && response.data) {
        const categories = response.data.map((categoryData: any) => ({
          _id: `category_${categoryData.category}`,
          name: categoryData.category,
          description: ''
        }));
        
        return categories;
      }
      
      return [];
    } catch (error) {
      console.error('Failed to fetch categories:', error);
      return [];
    }
  }

  /**
   * Fetch inventory from server
   */
  private async fetchInventory(companyId: string, locationId: string): Promise<any[]> {
    try {
      const response = await apiService.get(`/pos/inventory`);
      
      if (response.success && response.data) {
        return response.data.map((item: any) => ({
          _id: item.id,
          name: item.name,
          category: item.category,
          currentStock: item.currentStock,
          minStock: item.minStock,
          maxStock: item.maxStock,
          unit: item.unit,
          costPerUnit: item.costPerUnit,
          supplier: item.supplier,
          supplierContact: item.supplierContact,
          sku: item.sku,
          barcode: item.barcode,
          location: item.location,
          lastRestocked: item.lastRestocked ? new Date(item.lastRestocked) : undefined,
          expiryDate: item.expiryDate ? new Date(item.expiryDate) : undefined,
          notes: item.notes,
          isActive: item.isActive
        }));
      }
      
      return [];
    } catch (error) {
      console.error('Failed to fetch inventory:', error);
      return [];
    }
  }

  /**
   * Fetch users from server
   */
  private async fetchUsers(companyId: string, locationId: string): Promise<any[]> {
    try {
      const response = await apiService.get(`/pos/users`);
      
      if (response.success && response.data) {
        return response.data.map((user: any) => ({
          _id: user.id,
          name: user.name,
          pin: user.pin,
          role: user.role,
          isActive: user.isActive
        }));
      }
      
      return [];
    } catch (error) {
      console.error('Failed to fetch users:', error);
      return [];
    }
  }

  /**
   * Get sync status
   */
  async getSyncStatus(): Promise<{
    isOnline: boolean;
    isSyncing: boolean;
    pendingCount: number;
    lastSync?: Date;
  }> {
    const pendingCount = this.currentCompanyId && this.currentLocationId 
      ? await db.getPendingSyncCount(this.currentCompanyId, this.currentLocationId)
      : 0;

    const lastSync = this.currentCompanyId && this.currentLocationId
      ? await db.getAppState('lastSync', this.currentCompanyId, this.currentLocationId)
      : null;

    return {
      isOnline: navigator.onLine,
      isSyncing: this.isSyncing,
      pendingCount,
      lastSync: lastSync ? new Date(lastSync) : undefined
    };
  }

  /**
   * Force sync now
   */
  async forcSync(): Promise<void> {
    if (this.currentCompanyId && this.currentLocationId) {
      await this.performSync();
    }
  }
}

export const syncService = new SyncService();