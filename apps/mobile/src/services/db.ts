import Dexie, { Table } from 'dexie';
import { 
  Order, 
  OrderItem as FoodPrepAIOrderItem,
  OrderStatus,
  OrderType,
  PaymentMethod,
  PaymentStatus 
} from '../types/order';
import { 
  InventoryItem, 
  StockTransaction, 
  Supplier,
  InventoryAlert 
} from '../types/inventory';
import { 
  BranchInventoryItem, 
  BranchInventoryAlternative,
  Company,
  Location,
  POSUser 
} from '../types/api';

// Enhanced database interfaces for offline storage
export interface IOrder {
  id?: number;
  _id?: string; // MongoDB ID when synced
  orderNumber: string;
  customerName?: string;
  customerPhone?: string;
  customerEmail?: string;
  items: IOrderItem[];
  subtotal: number;
  tax: number;
  tip?: number;
  discount?: number;
  total: number;
  status: OrderStatus;
  orderType: OrderType;
  orderTime: Date;
  estimatedCompletion?: Date;
  completedTime?: Date;
  tableNumber?: number;
  deliveryAddress?: string;
  specialInstructions?: string;
  paymentMethod?: PaymentMethod;
  paymentStatus?: PaymentStatus;
  paymentTransactionId?: string;
  assignedStaff?: string;
  companyId: string;
  locationId: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  syncStatus: 'PENDING' | 'SYNCED' | 'FAILED';
  lastSyncAt?: Date;
}

export interface IOrderItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  modifiers?: Array<{
    name: string;
    price: number;
  }>;
  specialInstructions?: string;
}

export interface IProduct {
  id?: number;
  _id?: string; // MongoDB ID
  name: string;
  description?: string;
  category: string;
  price: number;
  cost?: number;
  isAvailable: boolean;
  stockLevel?: number;
  unit?: string;
  imageUrl?: string;
  companyId: string;
  locationId: string;
  alternatives?: IProductAlternative[];
  syncStatus: 'PENDING' | 'SYNCED';
  lastSyncAt?: Date;
}

export interface IProductAlternative {
  id: string;
  name: string;
  description?: string;
  price: number;
  isAvailable: boolean;
}

export interface ICategory {
  id?: number;
  _id?: string; // MongoDB ID
  name: string;
  description?: string;
  companyId: string;
  locationId: string;
  syncStatus: 'PENDING' | 'SYNCED';
  lastSyncAt?: Date;
}

export interface IInventoryItem {
  id?: number;
  _id?: string; // MongoDB ID
  name: string;
  category: string;
  currentStock: number;
  minStock: number;
  maxStock: number;
  unit: string;
  costPerUnit: number;
  supplier?: string;
  supplierContact?: string;
  sku?: string;
  barcode?: string;
  location?: string;
  lastRestocked?: Date;
  expiryDate?: Date;
  notes?: string;
  isActive: boolean;
  companyId: string;
  locationId: string;
  syncStatus: 'PENDING' | 'SYNCED';
  lastSyncAt?: Date;
}

export interface IStockTransaction {
  id?: number;
  _id?: string; // MongoDB ID
  inventoryItemId: string;
  type: 'restock' | 'usage' | 'waste' | 'adjustment';
  quantity: number;
  reason?: string;
  performedBy: string;
  timestamp: Date;
  cost?: number;
  companyId: string;
  locationId: string;
  syncStatus: 'PENDING' | 'SYNCED';
  lastSyncAt?: Date;
}

export interface IUser {
  id?: number;
  _id?: string; // MongoDB ID
  name: string;
  pin: string;
  role: 'cashier' | 'manager' | 'admin';
  companyId: string;
  locationId: string;
  isActive: boolean;
  syncStatus: 'PENDING' | 'SYNCED';
  lastSyncAt?: Date;
}

export interface ICompany {
  id?: number;
  _id?: string; // MongoDB ID
  name: string;
  description?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  syncStatus: 'PENDING' | 'SYNCED';
  lastSyncAt?: Date;
}

export interface ILocation {
  id?: number;
  _id?: string; // MongoDB ID
  name: string;
  address: string;
  phone?: string;
  email?: string;
  companyId: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  syncStatus: 'PENDING' | 'SYNCED';
  lastSyncAt?: Date;
}

export interface ISyncQueue {
  id?: number;
  operation: 'CREATE' | 'UPDATE' | 'DELETE';
  entityType: 'order' | 'product' | 'category' | 'inventory' | 'transaction' | 'user';
  entityId: string;
  data: any;
  attempts: number;
  lastAttempt?: Date;
  status: 'PENDING' | 'PROCESSING' | 'FAILED';
  companyId: string;
  locationId: string;
  createdAt: Date;
}

export interface IAppState {
  id?: number;
  key: string;
  value: any;
  companyId?: string;
  locationId?: string;
  updatedAt: Date;
}

class FoodPrepAIDatabase extends Dexie {
  orders!: Table<IOrder>;
  products!: Table<IProduct>;
  categories!: Table<ICategory>;
  inventory!: Table<IInventoryItem>;
  stockTransactions!: Table<IStockTransaction>;
  users!: Table<IUser>;
  companies!: Table<ICompany>;
  locations!: Table<ILocation>;
  syncQueue!: Table<ISyncQueue>;
  appState!: Table<IAppState>;

  constructor() {
    super('foodPrepAIDB');
    this.version(1).stores({
      orders: '++id, _id, orderNumber, status, orderType, companyId, locationId, syncStatus, createdAt, orderTime',
      products: '++id, _id, name, category, companyId, locationId, syncStatus, isAvailable',
      categories: '++id, _id, name, companyId, locationId, syncStatus',
      inventory: '++id, _id, name, category, companyId, locationId, syncStatus, currentStock, isActive',
      stockTransactions: '++id, _id, inventoryItemId, type, companyId, locationId, syncStatus, timestamp',
      users: '++id, _id, name, pin, role, companyId, locationId, syncStatus, isActive',
      companies: '++id, _id, name, syncStatus, isActive',
      locations: '++id, _id, name, companyId, syncStatus, isActive',
      syncQueue: '++id, operation, entityType, entityId, status, companyId, locationId, createdAt',
      appState: '++id, key, companyId, locationId, updatedAt'
    });
  }

  // Helper method to get app state
  async getAppState(key: string, companyId?: string, locationId?: string): Promise<any> {
    let query = this.appState.where('key').equals(key);
    
    if (companyId) {
      query = query.and(item => item.companyId === companyId);
    }
    
    if (locationId) {
      query = query.and(item => item.locationId === locationId);
    }

    const state = await query.first();
    return state?.value;
  }

  // Helper method to set app state
  async setAppState(key: string, value: any, companyId?: string, locationId?: string): Promise<void> {
    let query = this.appState.where('key').equals(key);
    
    if (companyId) {
      query = query.and(item => item.companyId === companyId);
    }
    
    if (locationId) {
      query = query.and(item => item.locationId === locationId);
    }

    const existing = await query.first();
    
    if (existing) {
      await this.appState.where('id').equals(existing.id!).modify({
        value,
        updatedAt: new Date()
      });
    } else {
      await this.appState.add({
        key,
        value,
        companyId,
        locationId,
        updatedAt: new Date()
      });
    }
  }

  // Add to sync queue
  async addToSyncQueue(
    operation: 'CREATE' | 'UPDATE' | 'DELETE',
    entityType: 'order' | 'product' | 'category' | 'inventory' | 'transaction' | 'user',
    entityId: string,
    data: any,
    companyId: string,
    locationId: string
  ): Promise<void> {
    await this.syncQueue.add({
      operation,
      entityType,
      entityId,
      data,
      attempts: 0,
      status: 'PENDING',
      companyId,
      locationId,
      createdAt: new Date()
    });
  }

  // Process sync queue for specific company/location
  async processSyncQueue(companyId: string, locationId: string): Promise<void> {
    const pendingItems = await this.syncQueue
      .where('status')
      .equals('PENDING')
      .filter(item => 
        item.companyId === companyId && 
        item.locationId === locationId && 
        item.attempts < 3
      )
      .toArray();

    for (const item of pendingItems) {
      try {
        await this.syncQueue.where('id').equals(item.id!).modify({ status: 'PROCESSING' });
        
        // Build API endpoint based on entity type
        const apiEndpoint = this.buildApiEndpoint(item.entityType, item.operation);
        
        const response = await fetch(apiEndpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-company-id': companyId,
            'x-location-id': locationId,
          },
          body: JSON.stringify(item.data),
        });

        if (!response.ok) {
          throw new Error(`Sync failed: ${response.statusText}`);
        }

        const result = await response.json();
        
        // Update the local entity with the server response if needed
        if (result.data && item.operation === 'CREATE') {
          await this.updateLocalEntity(item.entityType, item.entityId, result.data);
        }

        // If successful, remove from queue
        await this.syncQueue.where('id').equals(item.id!).delete();

      } catch (error) {
        console.error(`Sync failed for ${item.entityType} ${item.entityId}:`, error);
        
        // If failed, increment attempts and mark as failed
        await this.syncQueue.where('id').equals(item.id!).modify({
          status: 'FAILED',
          attempts: item.attempts + 1,
          lastAttempt: new Date()
        });
      }
    }
  }

  private buildApiEndpoint(entityType: string, operation: string): string {
    const baseUrl = '/api/pos';
    
    switch (entityType) {
      case 'order':
        return `${baseUrl}/orders/${operation.toLowerCase()}`;
      case 'product':
        return `${baseUrl}/products/${operation.toLowerCase()}`;
      case 'category':
        return `${baseUrl}/categories/${operation.toLowerCase()}`;
      case 'inventory':
        return `${baseUrl}/inventory/${operation.toLowerCase()}`;
      case 'transaction':
        return `${baseUrl}/transactions/${operation.toLowerCase()}`;
      case 'user':
        return `${baseUrl}/users/${operation.toLowerCase()}`;
      default:
        throw new Error(`Unknown entity type: ${entityType}`);
    }
  }

  private async updateLocalEntity(entityType: string, entityId: string, serverData: any): Promise<void> {
    try {
      switch (entityType) {
        case 'order':
          await this.orders.where('_id').equals(entityId).modify({
            _id: serverData._id,
            syncStatus: 'SYNCED',
            lastSyncAt: new Date()
          });
          break;
        case 'product':
          await this.products.where('_id').equals(entityId).modify({
            _id: serverData._id,
            syncStatus: 'SYNCED',
            lastSyncAt: new Date()
          });
          break;
        case 'category':
          await this.categories.where('_id').equals(entityId).modify({
            _id: serverData._id,
            syncStatus: 'SYNCED',
            lastSyncAt: new Date()
          });
          break;
        case 'inventory':
          await this.inventory.where('_id').equals(entityId).modify({
            _id: serverData._id,
            syncStatus: 'SYNCED',
            lastSyncAt: new Date()
          });
          break;
        case 'transaction':
          await this.stockTransactions.where('_id').equals(entityId).modify({
            _id: serverData._id,
            syncStatus: 'SYNCED',
            lastSyncAt: new Date()
          });
          break;
        case 'user':
          await this.users.where('_id').equals(entityId).modify({
            _id: serverData._id,
            syncStatus: 'SYNCED',
            lastSyncAt: new Date()
          });
          break;
      }
    } catch (error) {
      console.error(`Failed to update local entity ${entityType} ${entityId}:`, error);
    }
  }

  // Get pending sync count for a company/location
  async getPendingSyncCount(companyId: string, locationId: string): Promise<number> {
    return await this.syncQueue
      .where('status')
      .equals('PENDING')
      .filter(item => 
        item.companyId === companyId && 
        item.locationId === locationId
      )
      .count();
  }

  // Clear all data for a specific company/location (useful for logout)
  async clearCompanyData(companyId: string, locationId: string): Promise<void> {
    await Promise.all([
      this.orders.where('companyId').equals(companyId).and(order => order.locationId === locationId).delete(),
      this.products.where('companyId').equals(companyId).and(product => product.locationId === locationId).delete(),
      this.categories.where('companyId').equals(companyId).and(category => category.locationId === locationId).delete(),
      this.inventory.where('companyId').equals(companyId).and(item => item.locationId === locationId).delete(),
      this.stockTransactions.where('companyId').equals(companyId).and(transaction => transaction.locationId === locationId).delete(),
      this.users.where('companyId').equals(companyId).and(user => user.locationId === locationId).delete(),
      this.syncQueue.where('companyId').equals(companyId).and(item => item.locationId === locationId).delete(),
      this.appState.where('companyId').equals(companyId).and(state => state.locationId === locationId).delete()
    ]);
  }

  // Bulk import data from server
  async bulkImport(
    data: {
      products?: IProduct[];
      categories?: ICategory[];
      inventory?: IInventoryItem[];
      users?: IUser[];
    },
    companyId: string,
    locationId: string
  ): Promise<void> {
    try {
      await this.transaction('rw', [this.products, this.categories, this.inventory, this.users], async () => {
        if (data.products) {
          await this.products.bulkPut(data.products.map(product => ({
            ...product,
            companyId,
            locationId,
            syncStatus: 'SYNCED' as const,
            lastSyncAt: new Date()
          })));
        }

        if (data.categories) {
          await this.categories.bulkPut(data.categories.map(category => ({
            ...category,
            companyId,
            locationId,
            syncStatus: 'SYNCED' as const,
            lastSyncAt: new Date()
          })));
        }

        if (data.inventory) {
          await this.inventory.bulkPut(data.inventory.map(item => ({
            ...item,
            companyId,
            locationId,
            syncStatus: 'SYNCED' as const,
            lastSyncAt: new Date()
          })));
        }

        if (data.users) {
          await this.users.bulkPut(data.users.map(user => ({
            ...user,
            companyId,
            locationId,
            syncStatus: 'SYNCED' as const,
            lastSyncAt: new Date()
          })));
        }
      });
    } catch (error) {
      console.error('Bulk import failed:', error);
      throw error;
    }
  }
}

export const db = new FoodPrepAIDatabase();