import { apiService } from './api';
import { 
  InventoryItem, 
  BranchInventoryItem, 
  BranchInventoryCategory, 
  StockTransaction, 
  InventoryMovement,
  StockCountSession,
  StockCountItem,
  WastageRecord,
  Supplier, 
  InventoryAlert 
} from '@/types/inventory';
import { ApiResponse, PaginatedResponse, BranchInventoryResponse } from '@/types/api';

export class InventoryService {
  private endpoint = '/inventory';
  private posEndpoint = '/pos';

  // FoodPrepAI Branch Inventory API
  async getBranchInventories(): Promise<BranchInventoryResponse> {
    return apiService.get<BranchInventoryCategory[]>(`${this.posEndpoint}/branch-inventories`);
  }

  async getBranchInventoryByCategory(category: string): Promise<ApiResponse<BranchInventoryItem[]>> {
    return apiService.get<BranchInventoryItem[]>(`${this.posEndpoint}/branch-inventories?category=${encodeURIComponent(category)}`);
  }

  async searchBranchInventory(query: string): Promise<ApiResponse<BranchInventoryItem[]>> {
    return apiService.get<BranchInventoryItem[]>(`${this.posEndpoint}/branch-inventories/search?q=${encodeURIComponent(query)}`);
  }

  async getBranchInventoryItem(id: string): Promise<ApiResponse<BranchInventoryItem>> {
    return apiService.get<BranchInventoryItem>(`${this.posEndpoint}/branch-inventories/items/${id}`);
  }

  async getBranchInventoryByBarcode(barcode: string): Promise<ApiResponse<BranchInventoryItem>> {
    return apiService.get<BranchInventoryItem>(`${this.posEndpoint}/branch-inventories/barcode/${barcode}`);
  }

  // Inventory Movement API (FoodPrepAI specific)
  async submitInventoryMovement(companyId: string, movement: InventoryMovement): Promise<ApiResponse<any>> {
    return apiService.post<any>(`/company/${companyId}/inventory/movement/v2`, movement);
  }

  async submitBulkInventoryMovements(companyId: string, movements: InventoryMovement[]): Promise<ApiResponse<any>> {
    return apiService.post<any>(`/company/${companyId}/inventory/movement/v2/bulk`, { movements });
  }

  async getInventoryMovements(companyId: string, itemId?: string, limit?: number): Promise<ApiResponse<PaginatedResponse<StockTransaction>>> {
    let url = `/company/${companyId}/inventory/movement/v2`;
    const params = new URLSearchParams();
    if (itemId) params.append('itemId', itemId);
    if (limit) params.append('limit', limit.toString());
    if (params.toString()) url += `?${params.toString()}`;
    
    return apiService.get<PaginatedResponse<StockTransaction>>(url);
  }

  // Stock Count functionality
  async createStockCountSession(): Promise<ApiResponse<StockCountSession>> {
    const credentials = apiService.getStoredCredentials();
    const session: Partial<StockCountSession> = {
      startTime: new Date(),
      status: 'in_progress',
      performedBy: 'current_user', // Should be replaced with actual user info
      items: [],
      totalVariance: 0,
      companyId: credentials.companyId || '',
      locationId: credentials.locationId || '',
    };
    
    return apiService.post<StockCountSession>(`${this.endpoint}/stock-count/session`, session);
  }

  async updateStockCountSession(sessionId: string, session: Partial<StockCountSession>): Promise<ApiResponse<StockCountSession>> {
    return apiService.patch<StockCountSession>(`${this.endpoint}/stock-count/session/${sessionId}`, session);
  }

  async getStockCountSession(sessionId: string): Promise<ApiResponse<StockCountSession>> {
    return apiService.get<StockCountSession>(`${this.endpoint}/stock-count/session/${sessionId}`);
  }

  async submitStockCount(sessionId: string): Promise<ApiResponse<void>> {
    const session = await this.getStockCountSession(sessionId);
    if (!session.success || !session.data) {
      throw new Error('Stock count session not found');
    }

    const credentials = apiService.getStoredCredentials();
    if (!credentials.companyId) {
      throw new Error('Company ID not found');
    }

    // Convert stock count items to inventory movements
    const movements: InventoryMovement[] = session.data.items.map(item => ({
      itemId: item.inventoryItemId,
      quantity: item.variance,
      transactionType: 'COUNT',
      reason: item.reason || 'Stock count adjustment',
      unitOfMeasure: item.unit,
      notes: item.notes,
    }));

    // Submit movements to FoodPrepAI API
    if (movements.length > 0) {
      await this.submitBulkInventoryMovements(credentials.companyId, movements);
    }

    // Mark session as completed
    await this.updateStockCountSession(sessionId, {
      status: 'completed',
      endTime: new Date(),
    });

    return { success: true };
  }

  // Wastage functionality
  async recordWastage(wastage: Omit<WastageRecord, 'id' | 'timestamp'>): Promise<ApiResponse<WastageRecord>> {
    const credentials = apiService.getStoredCredentials();
    if (!credentials.companyId) {
      throw new Error('Company ID not found');
    }

    // Submit wastage as inventory movement
    const movement: InventoryMovement = {
      itemId: wastage.inventoryItemId,
      quantity: -Math.abs(wastage.quantity), // Negative for wastage
      transactionType: 'WASTAGE',
      reason: wastage.reason.label,
      reasonCode: wastage.reason.code,
      unitOfMeasure: wastage.unit,
      notes: wastage.notes,
      photoUrl: wastage.photoUrl,
      cost: wastage.cost,
    };

    await this.submitInventoryMovement(credentials.companyId, movement);

    // Return the wastage record
    const record: WastageRecord = {
      ...wastage,
      id: `wastage_${Date.now()}`,
      timestamp: new Date(),
    };

    return { success: true, data: record };
  }

  async getWastageRecords(companyId: string, itemId?: string): Promise<ApiResponse<WastageRecord[]>> {
    const movements = await this.getInventoryMovements(companyId, itemId);
    if (!movements.success || !movements.data) {
      return { success: false, error: 'Failed to fetch wastage records' };
    }

    const wastageRecords: WastageRecord[] = movements.data.items
      .filter(movement => movement.type === 'WASTAGE')
      .map(movement => ({
        id: movement.id,
        inventoryItemId: movement.inventoryItemId,
        quantity: Math.abs(movement.quantity),
        unit: movement.unitOfMeasure || '',
        reason: {
          code: movement.reasonCode?.code || 'OTHER',
          label: movement.reason || 'Unknown',
        },
        notes: movement.notes,
        photoUrl: movement.photoUrl,
        performedBy: movement.performedBy,
        timestamp: movement.timestamp,
        cost: movement.cost,
        companyId: movement.companyId || companyId,
        locationId: movement.locationId || '',
      }));

    return { success: true, data: wastageRecords };
  }

  // Legacy support methods
  async getInventoryItems(): Promise<ApiResponse<InventoryItem[]>> {
    const branchInventories = await this.getBranchInventories();
    if (!branchInventories.success || !branchInventories.data) {
      return { success: false, error: 'Failed to fetch inventory items' };
    }

    const items: InventoryItem[] = branchInventories.data.flatMap(category =>
      category.items.map(item => ({
        ...item,
        currentStock: item.stockLevel || 0,
        minStock: item.minStock || 0,
        maxStock: item.maxStock || 100,
        costPerUnit: item.cost || 0,
        isActive: item.isAvailable,
      }))
    );

    return { success: true, data: items };
  }

  async getInventoryItem(id: string): Promise<ApiResponse<InventoryItem>> {
    const branchItem = await this.getBranchInventoryItem(id);
    if (!branchItem.success || !branchItem.data) {
      return { success: false, error: 'Item not found' };
    }

    const item: InventoryItem = {
      ...branchItem.data,
      currentStock: branchItem.data.stockLevel || 0,
      minStock: branchItem.data.minStock || 0,
      maxStock: branchItem.data.maxStock || 100,
      costPerUnit: branchItem.data.cost || 0,
      isActive: branchItem.data.isAvailable,
    };

    return { success: true, data: item };
  }

  async updateStock(id: string, quantity: number, type: StockTransaction['type'], reason?: string): Promise<ApiResponse<InventoryItem>> {
    const credentials = apiService.getStoredCredentials();
    if (!credentials.companyId) {
      throw new Error('Company ID not found');
    }

    const movement: InventoryMovement = {
      itemId: id,
      quantity,
      transactionType: type.toUpperCase() as any,
      reason,
    };

    await this.submitInventoryMovement(credentials.companyId, movement);
    return this.getInventoryItem(id);
  }

  async getStockTransactions(itemId?: string): Promise<ApiResponse<PaginatedResponse<StockTransaction>>> {
    const credentials = apiService.getStoredCredentials();
    if (!credentials.companyId) {
      throw new Error('Company ID not found');
    }

    return this.getInventoryMovements(credentials.companyId, itemId);
  }

  async getLowStockItems(): Promise<ApiResponse<InventoryItem[]>> {
    const items = await this.getInventoryItems();
    if (!items.success || !items.data) {
      return { success: false, error: 'Failed to fetch inventory items' };
    }

    const lowStockItems = items.data.filter(item => 
      item.currentStock <= item.minStock && item.isActive
    );

    return { success: true, data: lowStockItems };
  }

  async getExpiringItems(days: number = 7): Promise<ApiResponse<InventoryItem[]>> {
    const items = await this.getInventoryItems();
    if (!items.success || !items.data) {
      return { success: false, error: 'Failed to fetch inventory items' };
    }

    const expiryThreshold = new Date();
    expiryThreshold.setDate(expiryThreshold.getDate() + days);

    const expiringItems = items.data.filter(item => 
      item.expiryDate && 
      new Date(item.expiryDate) <= expiryThreshold &&
      item.isActive
    );

    return { success: true, data: expiringItems };
  }

  async getSuppliers(): Promise<ApiResponse<Supplier[]>> {
    return apiService.get<Supplier[]>(`${this.endpoint}/suppliers`);
  }

  async createSupplier(supplier: Omit<Supplier, 'id'>): Promise<ApiResponse<Supplier>> {
    return apiService.post<Supplier>(`${this.endpoint}/suppliers`, supplier);
  }

  async updateSupplier(id: string, supplier: Partial<Supplier>): Promise<ApiResponse<Supplier>> {
    return apiService.put<Supplier>(`${this.endpoint}/suppliers/${id}`, supplier);
  }

  async deleteSupplier(id: string): Promise<ApiResponse<void>> {
    return apiService.delete<void>(`${this.endpoint}/suppliers/${id}`);
  }

  async getAlerts(): Promise<ApiResponse<InventoryAlert[]>> {
    return apiService.get<InventoryAlert[]>(`${this.endpoint}/alerts`);
  }

  async markAlertResolved(alertId: string): Promise<ApiResponse<InventoryAlert>> {
    return apiService.patch<InventoryAlert>(`${this.endpoint}/alerts/${alertId}/resolve`, {});
  }

  async generateStockReport(dateRange: { start: Date; end: Date }): Promise<ApiResponse<any>> {
    return apiService.post<any>(`${this.endpoint}/reports/stock`, {
      startDate: dateRange.start.toISOString(),
      endDate: dateRange.end.toISOString()
    });
  }
}

export const inventoryService = new InventoryService();