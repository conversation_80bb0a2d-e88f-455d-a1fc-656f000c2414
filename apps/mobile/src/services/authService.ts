import { apiService, ApiError } from './api';
import { 
  PinLoginRequest, 
  PinLoginResponse, 
  POSUser, 
  Company, 
  Location,
  ApiResponse 
} from '@/types/api';

export class AuthService {
  private currentUser: POSUser | null = null;
  private companies: Company[] = [];
  private locations: Location[] = [];

  constructor() {
    this.loadStoredUser();
  }

  private loadStoredUser() {
    try {
      const storedUser = localStorage.getItem('foodprepai_user');
      if (storedUser) {
        this.currentUser = JSON.parse(storedUser);
      }
    } catch (error) {
      console.warn('Failed to load stored user:', error);
    }
  }

  private storeUser(user: POSUser) {
    try {
      localStorage.setItem('foodprepai_user', JSON.stringify(user));
      this.currentUser = user;
    } catch (error) {
      console.warn('Failed to store user:', error);
    }
  }

  private clearStoredUser() {
    try {
      localStorage.removeItem('foodprepai_user');
      this.currentUser = null;
    } catch (error) {
      console.warn('Failed to clear stored user:', error);
    }
  }

  /**
   * Get list of available companies
   */
  async getCompanies(): Promise<Company[]> {
    try {
      const response = await apiService.get<Company[]>('/pos/companies');
      if (response.success && response.data) {
        this.companies = response.data;
        return response.data;
      }
      throw new Error(response.message || 'Failed to fetch companies');
    } catch (error) {
      console.error('Failed to get companies:', error);
      throw error;
    }
  }

  /**
   * Get list of locations for a specific company
   */
  async getLocations(companyId: string): Promise<Location[]> {
    try {
      const response = await apiService.get<Location[]>(`/pos/companies/${companyId}/locations`);
      if (response.success && response.data) {
        this.locations = response.data;
        return response.data;
      }
      throw new Error(response.message || 'Failed to fetch locations');
    } catch (error) {
      console.error('Failed to get locations:', error);
      throw error;
    }
  }

  /**
   * Authenticate user with PIN
   */
  async loginWithPin(pin: string, companyId: string, locationId: string): Promise<POSUser> {
    try {
      const loginData: PinLoginRequest = {
        pin,
        companyId,
        locationId
      };

      // Set company and location before making the request
      apiService.setCompanyAndLocation(companyId, locationId);

      const response = await apiService.post<PinLoginResponse>('/pos/auth/pin-login', loginData);
      
      if (response.success && response.data) {
        const { token, user } = response.data;
        
        // Store the JWT token
        apiService.setAuthToken(token);
        
        // Store user information
        this.storeUser(user);
        
        return user;
      }
      
      throw new Error(response.message || 'Login failed');
    } catch (error) {
      console.error('PIN login failed:', error);
      
      if (error instanceof ApiError) {
        if (error.isAuthError) {
          throw new Error('Invalid PIN or unauthorized access');
        } else if (error.isValidationError) {
          throw new Error('Invalid PIN format or missing required fields');
        } else if (error.isNetworkError) {
          throw new Error('Network error. Please check your connection and try again.');
        } else if (error.isServerError) {
          throw new Error('Server error. Please try again later.');
        }
      }
      
      throw error;
    }
  }

  /**
   * Logout current user
   */
  async logout(): Promise<void> {
    try {
      // Clear stored user data
      this.clearStoredUser();
      
      // Clear API service credentials
      apiService.removeAuthToken();
      apiService.clearCompanyAndLocation();
      
      // Optionally call logout endpoint if it exists
      try {
        await apiService.post('/pos/auth/logout');
      } catch (error) {
        // Ignore logout endpoint errors as local cleanup is more important
        console.warn('Logout endpoint failed, but local cleanup completed:', error);
      }
    } catch (error) {
      console.error('Logout failed:', error);
      throw error;
    }
  }

  /**
   * Validate current authentication status
   */
  async validateToken(): Promise<boolean> {
    try {
      const credentials = apiService.getStoredCredentials();
      
      if (!credentials.token || !credentials.companyId || !credentials.locationId) {
        return false;
      }

      // Try to make an authenticated request to validate the token
      const response = await apiService.get('/pos/auth/validate');
      return response.success;
    } catch (error) {
      console.error('Token validation failed:', error);
      
      if (error instanceof ApiError && error.isAuthError) {
        // Token is invalid, clear it
        await this.logout();
      }
      
      return false;
    }
  }

  /**
   * Get current authenticated user
   */
  getCurrentUser(): POSUser | null {
    return this.currentUser;
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    const credentials = apiService.getStoredCredentials();
    return !!(this.currentUser && credentials.token && credentials.companyId && credentials.locationId);
  }

  /**
   * Get current company and location IDs
   */
  getCurrentContext(): { companyId: string | null; locationId: string | null } {
    const credentials = apiService.getStoredCredentials();
    return {
      companyId: credentials.companyId,
      locationId: credentials.locationId
    };
  }

  /**
   * Check if user has a specific role
   */
  hasRole(role: 'cashier' | 'manager' | 'admin'): boolean {
    return this.currentUser?.role === role;
  }

  /**
   * Check if user has manager or admin privileges
   */
  hasManagerPrivileges(): boolean {
    return this.currentUser?.role === 'manager' || this.currentUser?.role === 'admin';
  }

  /**
   * Check if user has admin privileges
   */
  hasAdminPrivileges(): boolean {
    return this.currentUser?.role === 'admin';
  }

  /**
   * Get cached companies
   */
  getCachedCompanies(): Company[] {
    return this.companies;
  }

  /**
   * Get cached locations
   */
  getCachedLocations(): Location[] {
    return this.locations;
  }
}

export const authService = new AuthService();