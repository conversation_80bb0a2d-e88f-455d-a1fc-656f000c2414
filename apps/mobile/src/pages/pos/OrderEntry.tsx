import React, { useState, useEffect, useRef } from 'react';
import {
  IonContent,
  IonHeader,
  IonPage,
  IonTitle,
  IonToolbar,
  IonSearchbar,
  IonSegment,
  IonSegmentButton,
  IonLabel,
  IonIcon,
  IonButton,
  IonGrid,
  IonRow,
  IonCol,
  IonChip,
  IonToast,
  IonAlert,
  IonFab,
  IonFabButton,
  IonRefresher,
  IonRefresherContent,
  IonLoading,
  IonInfiniteScroll,
  IonInfiniteScrollContent,
  IonBadge,
  IonMenuButton,
} from '@ionic/react';
import {
  search,
  filter,
  cart,
  barcode,
  refresh,
  gridOutline,
  listOutline,
  chevronDown,
  warning,
  checkmarkCircle,
} from 'ionicons/icons';
import { useCart } from '@/hooks/useCart';
import { useEnhancedAuth } from '@/contexts/EnhancedAuthContext';
import { menuService } from '@/services/menuService';
import { posService } from '@/services/posService';
import { BranchInventoryCategory, BranchInventoryItem } from '@/types/api';
import { ProductCard, CartSummary } from '@/components/pos';
import { LoadingSpinner } from '@/components';
import { BarcodeScanner } from '@capacitor-community/barcode-scanner';

interface OrderEntryProps {
  onProceedToPayment: (items: any[]) => void;
  onBack?: () => void;
}

const OrderEntry: React.FC<OrderEntryProps> = ({
  onProceedToPayment,
  onBack,
}) => {
  // Hooks
  const {
    cartItems,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    getSubtotal,
    getTax,
    getTotalPrice,
    validateCartStock,
    cartErrors,
    clearErrors,
  } = useCart();

  const { currentUser, company, location, isOnline } = useEnhancedAuth();

  // State
  const [categories, setCategories] = useState<BranchInventoryCategory[]>([]);
  const [filteredItems, setFilteredItems] = useState<BranchInventoryItem[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [showClearAlert, setShowClearAlert] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showScanner, setShowScanner] = useState(false);
  const [stockValidationErrors, setStockValidationErrors] = useState<string[]>([]);
  
  const searchRef = useRef<HTMLIonSearchbarElement>(null);

  // Load inventory data
  useEffect(() => {
    loadInventory();
  }, [company, location]);

  // Filter items when search or category changes
  useEffect(() => {
    filterItems();
  }, [categories, selectedCategory, searchQuery]);

  // Clear errors when cart changes
  useEffect(() => {
    clearErrors();
    setStockValidationErrors([]);
  }, [cartItems, clearErrors]);

  const loadInventory = async () => {
    try {
      setIsLoading(true);
      const inventoryData = await menuService.getBranchInventory();
      setCategories(inventoryData);
    } catch (error) {
      console.error('Failed to load inventory:', error);
      showToastMessage('Failed to load inventory. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const filterItems = () => {
    let items: BranchInventoryItem[] = [];

    if (selectedCategory === 'all') {
      categories.forEach(category => {
        items.push(...category.items);
      });
    } else {
      const category = categories.find(cat => cat.category === selectedCategory);
      if (category) {
        items = [...category.items];
      }
    }

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      items = items.filter(item =>
        item.name.toLowerCase().includes(query) ||
        item.description?.toLowerCase().includes(query) ||
        item.category.toLowerCase().includes(query)
      );
    }

    // Sort by availability and name
    items.sort((a, b) => {
      if (a.isAvailable && !b.isAvailable) return -1;
      if (!a.isAvailable && b.isAvailable) return 1;
      return a.name.localeCompare(b.name);
    });

    setFilteredItems(items);
  };

  const handleAddToCart = async (item: BranchInventoryItem) => {
    try {
      const success = await addToCart(item, 1);
      if (success) {
        showToastMessage(`${item.name} added to cart`);
      }
    } catch (error) {
      console.error('Failed to add item to cart:', error);
      showToastMessage('Failed to add item to cart');
    }
  };

  const handleCartQuantityUpdate = (itemId: string, quantity: number, timestamp?: number) => {
    updateQuantity(itemId, quantity, timestamp);
  };

  const handleCartItemRemove = (itemId: string, timestamp?: number) => {
    removeFromCart(itemId, timestamp);
  };

  const handleClearCart = () => {
    setShowClearAlert(true);
  };

  const confirmClearCart = () => {
    clearCart();
    setShowClearAlert(false);
    showToastMessage('Cart cleared');
  };

  const handleRefresh = async (event?: CustomEvent) => {
    setIsRefreshing(true);
    try {
      await loadInventory();
      showToastMessage('Inventory refreshed');
    } catch (error) {
      showToastMessage('Failed to refresh inventory');
    } finally {
      setIsRefreshing(false);
      if (event) {
        event.detail.complete();
      }
    }
  };

  const handleProceedToPayment = async () => {
    if (cartItems.length === 0) {
      showToastMessage('Please add items to cart before proceeding');
      return;
    }

    try {
      // Validate stock before proceeding
      const validation = await validateCartStock();
      if (!validation.isValid) {
        setStockValidationErrors(validation.issues);
        showToastMessage('Please resolve stock issues before proceeding');
        return;
      }

      onProceedToPayment(cartItems);
    } catch (error) {
      console.error('Error validating cart:', error);
      showToastMessage('Failed to validate cart. Please try again.');
    }
  };

  const handleBarcodeSearch = async () => {
    try {
      // Check permissions
      const status = await BarcodeScanner.checkPermission({ force: true });
      
      if (status.granted) {
        setShowScanner(true);
        
        // Start scanning
        const result = await BarcodeScanner.startScan();
        
        if (result.hasContent) {
          // Search for product by barcode
          setSearchQuery(result.content);
          showToastMessage(`Scanned: ${result.content}`);
        }
      } else {
        showToastMessage('Camera permission is required for barcode scanning');
      }
    } catch (error) {
      console.error('Barcode scanning error:', error);
      showToastMessage('Failed to scan barcode');
    } finally {
      setShowScanner(false);
      BarcodeScanner.stopScan();
    }
  };

  const showToastMessage = (message: string) => {
    setToastMessage(message);
    setShowToast(true);
  };

  const getCartQuantity = (itemId: string): number => {
    return cartItems
      .filter(item => item.id === itemId)
      .reduce((total, item) => total + item.quantity, 0);
  };

  const getCategoryList = () => {
    const allCategories = ['all', ...categories.map(cat => cat.category)];
    return allCategories;
  };

  if (isLoading) {
    return (
      <IonPage>
        <IonContent>
          <LoadingSpinner 
            fullscreen 
            message="Loading product catalog..." 
            size="large"
          />
        </IonContent>
      </IonPage>
    );
  }

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar color="primary">
          <IonMenuButton slot="start" />
          <IonTitle>Order Entry</IonTitle>
          {onBack && (
            <IonButton fill="clear" slot="start" onClick={onBack}>
              Back
            </IonButton>
          )}
          <div slot="end" className="flex items-center space-x-2 mr-4">
            {/* Cart badge */}
            {cartItems.length > 0 && (
              <IonButton fill="clear" onClick={handleProceedToPayment}>
                <IonIcon icon={cart} />
                <IonBadge color="primary">{cartItems.length}</IonBadge>
              </IonButton>
            )}
            
            {/* View mode toggle */}
            <IonButton
              fill="clear"
              onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
            >
              <IonIcon icon={viewMode === 'grid' ? listOutline : gridOutline} />
            </IonButton>
          </div>
        </IonToolbar>
        
        {/* Search and filters */}
        <IonToolbar color="light">
          <div className="px-4 py-2">
            <div className="flex items-center space-x-2 mb-3">
              <IonSearchbar
                ref={searchRef}
                value={searchQuery}
                onIonInput={(e) => setSearchQuery(e.detail.value!)}
                placeholder="Search products..."
                showClearButton="focus"
                className="flex-1"
              />
              
              <IonButton
                fill="outline"
                size="default"
                onClick={handleBarcodeSearch}
                disabled={!isOnline}
              >
                <IonIcon icon={barcode} slot="icon-only" />
              </IonButton>
            </div>
            
            {/* Category filter */}
            <IonSegment
              value={selectedCategory}
              onIonChange={(e) => setSelectedCategory(e.detail.value as string)}
              scrollable
            >
              {getCategoryList().map(category => (
                <IonSegmentButton key={category} value={category}>
                  <IonLabel className="text-sm">
                    {category === 'all' ? 'All Categories' : category}
                  </IonLabel>
                </IonSegmentButton>
              ))}
            </IonSegment>
          </div>
        </IonToolbar>
      </IonHeader>

      <IonContent fullscreen>
        <IonRefresher slot="fixed" onIonRefresh={handleRefresh}>
          <IonRefresherContent
            pullingIcon={chevronDown}
            pullingText="Pull to refresh inventory"
            refreshingSpinner="crescent"
            refreshingText="Refreshing..."
          />
        </IonRefresher>

        {/* Error messages */}
        {(cartErrors.length > 0 || stockValidationErrors.length > 0) && (
          <div className="p-4 bg-red-50 border-b border-red-200">
            {cartErrors.map((error, index) => (
              <div key={index} className="flex items-center text-red-700 text-sm mb-1">
                <IonIcon icon={warning} className="mr-2" />
                {error}
              </div>
            ))}
            {stockValidationErrors.map((error, index) => (
              <div key={index} className="flex items-center text-red-700 text-sm mb-1">
                <IonIcon icon={warning} className="mr-2" />
                {error}
              </div>
            ))}
          </div>
        )}

        <IonGrid>
          <IonRow>
            {/* Product catalog */}
            <IonCol size="12" sizeLg="8">
              <div className="p-4">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-semibold">Products</h2>
                  <div className="flex items-center space-x-2">
                    {!isOnline && (
                      <IonChip color="warning" size="small">
                        Offline Mode
                      </IonChip>
                    )}
                    <span className="text-sm text-gray-500">
                      {filteredItems.length} items
                    </span>
                  </div>
                </div>

                {filteredItems.length === 0 ? (
                  <div className="text-center py-12">
                    <IonIcon 
                      icon={search} 
                      className="text-4xl text-gray-400 mb-4" 
                    />
                    <p className="text-gray-500 mb-2">No products found</p>
                    {searchQuery && (
                      <p className="text-sm text-gray-400 mb-4">
                        Try adjusting your search or category filter
                      </p>
                    )}
                    <IonButton
                      fill="outline"
                      onClick={() => {
                        setSearchQuery('');
                        setSelectedCategory('all');
                      }}
                    >
                      Clear Filters
                    </IonButton>
                  </div>
                ) : (
                  <div className={viewMode === 'grid' 
                    ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4"
                    : "space-y-2"
                  }>
                    {filteredItems.map((item) => (
                      <ProductCard
                        key={item.id}
                        item={item}
                        onAddToCart={handleAddToCart}
                        cartQuantity={getCartQuantity(item.id)}
                        compact={viewMode === 'list'}
                        showStock={true}
                        showCategory={selectedCategory === 'all'}
                      />
                    ))}
                  </div>
                )}
              </div>
            </IonCol>

            {/* Cart sidebar */}
            <IonCol size="12" sizeLg="4">
              <div className="p-4 lg:sticky lg:top-0">
                <CartSummary
                  items={cartItems}
                  subtotal={getSubtotal()}
                  tax={getTax()}
                  total={getTotalPrice()}
                  onCheckout={handleProceedToPayment}
                  onClearCart={handleClearCart}
                  checkoutDisabled={cartItems.length === 0}
                  errors={[...cartErrors, ...stockValidationErrors]}
                />

                {/* Cart items details */}
                {cartItems.length > 0 && (
                  <div className="mt-4">
                    <h3 className="text-lg font-semibold mb-3">Cart Items</h3>
                    <div className="space-y-2">
                      {cartItems.map((item) => (
                        <div
                          key={`${item.id}-${item.timestamp}`}
                          className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                        >
                          <div className="flex-1">
                            <h4 className="font-medium text-sm">{item.name}</h4>
                            <p className="text-xs text-gray-600">
                              ${item.price.toFixed(2)} × {item.quantity}
                            </p>
                          </div>
                          <div className="flex items-center space-x-2">
                            <IonButton
                              size="small"
                              fill="clear"
                              onClick={() => handleCartQuantityUpdate(item.id, item.quantity - 1, item.timestamp)}
                              disabled={item.quantity <= 1}
                            >
                              <IonIcon icon={remove} />
                            </IonButton>
                            <span className="text-sm font-medium min-w-[20px] text-center">
                              {item.quantity}
                            </span>
                            <IonButton
                              size="small"
                              fill="clear"
                              onClick={() => handleCartQuantityUpdate(item.id, item.quantity + 1, item.timestamp)}
                              disabled={item.stockLevel !== undefined && item.quantity >= item.stockLevel}
                            >
                              <IonIcon icon={add} />
                            </IonButton>
                          </div>
                          <div className="text-right ml-3">
                            <span className="text-sm font-semibold">
                              ${(item.price * item.quantity).toFixed(2)}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </IonCol>
          </IonRow>
        </IonGrid>

        {/* Floating action button for mobile */}
        {cartItems.length > 0 && (
          <IonFab vertical="bottom" horizontal="end" slot="fixed">
            <IonFabButton onClick={handleProceedToPayment}>
              <IonIcon icon={cart} />
              <IonBadge color="light" className="absolute -top-2 -right-2">
                {cartItems.length}
              </IonBadge>
            </IonFabButton>
          </IonFab>
        )}

        {/* Loading overlay for scanner */}
        <IonLoading
          isOpen={showScanner}
          message="Position barcode in the camera view"
          onDidDismiss={() => setShowScanner(false)}
        />

        {/* Clear cart confirmation */}
        <IonAlert
          isOpen={showClearAlert}
          onDidDismiss={() => setShowClearAlert(false)}
          header="Clear Cart"
          message="Are you sure you want to remove all items from the cart?"
          buttons={[
            {
              text: 'Cancel',
              role: 'cancel',
            },
            {
              text: 'Clear',
              role: 'destructive',
              handler: confirmClearCart,
            },
          ]}
        />

        {/* Toast messages */}
        <IonToast
          isOpen={showToast}
          onDidDismiss={() => setShowToast(false)}
          message={toastMessage}
          duration={3000}
          position="top"
        />
      </IonContent>
    </IonPage>
  );
};

export default OrderEntry;