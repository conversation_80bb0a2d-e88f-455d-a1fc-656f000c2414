import React, { useState, useEffect } from 'react';
import {
  IonContent,
  IonHeader,
  IonPage,
  IonTitle,
  IonToolbar,
  IonButton,
  IonIcon,
  IonGrid,
  IonRow,
  IonCol,
  IonChip,
  IonBadge,
  IonList,
  IonItem,
  IonLabel,
  IonRefresher,
  IonRefresherContent,
  IonToast,
  IonSegment,
  IonSegmentButton,
  IonNote,
  IonProgressBar,
  IonMenuButton,
} from '@ionic/react';
import {
  add,
  receipt,
  statsChart,
  card,
  cash,
  trending,
  people,
  bag,
  refresh,
  sync,
  wifi,
  wifiOff,
  chevronDown,
  chevronForward,
  todayOutline,
  person,
  business,
  location,
  phonePortrait,
} from 'ionicons/icons';
import { useHistory } from 'react-router-dom';
import { useEnhancedAuth } from '../../contexts/EnhancedAuthContext';
import { posService, DailySalesReport } from '../../services/posService';
import { Loading<PERSON>pin<PERSON>, <PERSON> } from '../../components';

interface QuickStat {
  title: string;
  value: string;
  icon: string;
  color: string;
  trend?: string;
}

const DashboardPage: React.FC = () => {
  const history = useHistory();
  const {
    currentUser,
    company,
    location,
    isOnline,
    isSyncing,
    syncStatus,
    forceSync,
  } = useEnhancedAuth();

  // State
  const [salesReport, setSalesReport] = useState<DailySalesReport | null>(null);
  const [recentTransactions, setRecentTransactions] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [selectedPeriod, setSelectedPeriod] = useState<'today' | 'week' | 'month'>('today');
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null);

  useEffect(() => {
    loadDashboardData();
    const interval = setInterval(loadDashboardData, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, [selectedPeriod]);

  const loadDashboardData = async () => {
    try {
      if (!isLoading) setIsLoading(true);
      
      const today = new Date().toISOString().split('T')[0];
      
      // Load sales report and recent transactions in parallel
      const [reportResponse, transactionsResponse] = await Promise.allSettled([
        posService.getDailySalesReport(today),
        posService.getRecentTransactions(10),
      ]);

      if (reportResponse.status === 'fulfilled') {
        setSalesReport(reportResponse.value);
      } else {
        console.error('Failed to load sales report:', reportResponse.reason);
      }

      if (transactionsResponse.status === 'fulfilled') {
        setRecentTransactions(transactionsResponse.value);
      } else {
        console.error('Failed to load recent transactions:', transactionsResponse.reason);
      }

      setLastSyncTime(new Date());
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
      showToastMessage('Failed to load dashboard data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async (event?: CustomEvent) => {
    try {
      if (isOnline) {
        await forceSync();
      }
      await loadDashboardData();
      showToastMessage('Dashboard refreshed');
    } catch (error) {
      console.error('Refresh failed:', error);
      showToastMessage('Failed to refresh dashboard');
    } finally {
      if (event) {
        event.detail.complete();
      }
    }
  };

  const showToastMessage = (message: string) => {
    setToastMessage(message);
    setShowToast(true);
  };

  const handleNewOrder = () => {
    history.push('/pos/order-entry');
  };

  const handleViewReports = () => {
    history.push('/reports');
  };

  const handleViewInventory = () => {
    history.push('/inventory');
  };

  const handleSettings = () => {
    history.push('/settings');
  };

  const getQuickStats = (): QuickStat[] => {
    if (!salesReport) return [];

    return [
      {
        title: 'Total Sales',
        value: `$${salesReport.totalSales.toFixed(2)}`,
        icon: trending,
        color: 'success',
        trend: '+12%',
      },
      {
        title: 'Orders',
        value: salesReport.totalOrders.toString(),
        icon: receipt,
        color: 'primary',
        trend: '+5%',
      },
      {
        title: 'Avg Order',
        value: `$${salesReport.averageOrderValue.toFixed(2)}`,
        icon: statsChart,
        color: 'warning',
        trend: '+8%',
      },
      {
        title: 'Customers',
        value: salesReport.totalOrders.toString(), // Assuming 1 order per customer for now
        icon: people,
        color: 'tertiary',
        trend: '+3%',
      },
    ];
  };

  const getPaymentMethodStats = () => {
    if (!salesReport) return [];

    const { paymentMethods } = salesReport;
    const total = Object.values(paymentMethods).reduce((sum, value) => sum + value, 0);

    return [
      {
        method: 'Cash',
        amount: paymentMethods.cash,
        percentage: total > 0 ? (paymentMethods.cash / total) * 100 : 0,
        icon: cash,
        color: 'success',
      },
      {
        method: 'Card',
        amount: paymentMethods.card,
        percentage: total > 0 ? (paymentMethods.card / total) * 100 : 0,
        icon: card,
        color: 'primary',
      },
      {
        method: 'Digital',
        amount: paymentMethods.digitalWallet,
        percentage: total > 0 ? (paymentMethods.digitalWallet / total) * 100 : 0,
        icon: phonePortrait,
        color: 'secondary',
      },
    ];
  };

  if (isLoading && !salesReport) {
    return (
      <IonPage>
        <IonContent>
          <LoadingSpinner 
            fullscreen 
            message="Loading dashboard..." 
            size="large"
          />
        </IonContent>
      </IonPage>
    );
  }

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar color="primary">
          <IonMenuButton slot="start" />
          <IonTitle>POS Dashboard</IonTitle>
          <div slot="end" className="flex items-center space-x-2 mr-4">
            {/* Sync status */}
            {syncStatus.pendingCount > 0 && (
              <IonChip color="warning" size="small">
                <IonIcon icon={sync} />
                <span className="ml-1">{syncStatus.pendingCount}</span>
              </IonChip>
            )}
            
            {/* Connection status */}
            <IonChip color={isOnline ? 'success' : 'warning'} size="small">
              <IonIcon icon={isOnline ? wifi : wifiOff} />
              <span className="ml-1">{isOnline ? 'Online' : 'Offline'}</span>
            </IonChip>
            
            {/* Refresh button */}
            <IonButton 
              fill="clear" 
              size="small"
              onClick={() => handleRefresh()}
              disabled={isSyncing}
            >
              <IonIcon icon={refresh} className={isSyncing ? 'animate-spin' : ''} />
            </IonButton>
          </div>
        </IonToolbar>
        
        {/* User info bar */}
        <IonToolbar color="light" className="min-h-[40px]">
          <div className="flex items-center justify-between px-4 py-1">
            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <div className="flex items-center">
                <IonIcon icon={person} className="mr-1" />
                <span>{currentUser?.name} ({currentUser?.role})</span>
              </div>
              <div className="flex items-center">
                <IonIcon icon={business} className="mr-1" />
                <span>{company?.name}</span>
              </div>
              <div className="flex items-center">
                <IonIcon icon={location} className="mr-1" />
                <span>{location?.name}</span>
              </div>
            </div>
            {lastSyncTime && (
              <div className="text-xs text-gray-500">
                Last sync: {lastSyncTime.toLocaleTimeString()}
              </div>
            )}
          </div>
        </IonToolbar>
      </IonHeader>

      <IonContent fullscreen>
        <IonRefresher slot="fixed" onIonRefresh={handleRefresh}>
          <IonRefresherContent
            pullingIcon={chevronDown}
            pullingText="Pull to refresh"
            refreshingSpinner="crescent"
            refreshingText="Refreshing..."
          />
        </IonRefresher>

        <div className="p-4">
          {/* Quick Actions */}
          <Card title="Quick Actions" className="mb-4">
            <IonGrid>
              <IonRow>
                <IonCol size="6" sizeMd="3">
                  <IonButton
                    expand="block"
                    fill="solid"
                    onClick={handleNewOrder}
                    className="h-20"
                  >
                    <div className="flex flex-col items-center">
                      <IonIcon icon={add} className="text-2xl mb-1" />
                      <span>New Order</span>
                    </div>
                  </IonButton>
                </IonCol>
                
                <IonCol size="6" sizeMd="3">
                  <IonButton
                    expand="block"
                    fill="outline"
                    onClick={handleViewReports}
                    className="h-20"
                  >
                    <div className="flex flex-col items-center">
                      <IonIcon icon={statsChart} className="text-2xl mb-1" />
                      <span>Reports</span>
                    </div>
                  </IonButton>
                </IonCol>
                
                <IonCol size="6" sizeMd="3">
                  <IonButton
                    expand="block"
                    fill="outline"
                    onClick={handleViewInventory}
                    className="h-20"
                  >
                    <div className="flex flex-col items-center">
                      <IonIcon icon={bag} className="text-2xl mb-1" />
                      <span>Inventory</span>
                    </div>
                  </IonButton>
                </IonCol>
                
                <IonCol size="6" sizeMd="3">
                  <IonButton
                    expand="block"
                    fill="outline"
                    onClick={handleSettings}
                    className="h-20"
                  >
                    <div className="flex flex-col items-center">
                      <IonIcon icon={person} className="text-2xl mb-1" />
                      <span>Settings</span>
                    </div>
                  </IonButton>
                </IonCol>
              </IonRow>
            </IonGrid>
          </Card>

          {/* Period selector */}
          <div className="mb-4">
            <IonSegment
              value={selectedPeriod}
              onIonChange={(e) => setSelectedPeriod(e.detail.value as any)}
            >
              <IonSegmentButton value="today">
                <IonLabel>Today</IonLabel>
              </IonSegmentButton>
              <IonSegmentButton value="week">
                <IonLabel>This Week</IonLabel>
              </IonSegmentButton>
              <IonSegmentButton value="month">
                <IonLabel>This Month</IonLabel>
              </IonSegmentButton>
            </IonSegment>
          </div>

          {/* Sales Overview */}
          <Card 
            title="Sales Overview" 
            subtitle={new Date().toLocaleDateString('en-US', { 
              weekday: 'long', 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric' 
            })}
            className="mb-4"
          >
            <IonGrid>
              <IonRow>
                {getQuickStats().map((stat, index) => (
                  <IonCol key={index} size="6" sizeMd="3">
                    <div className="text-center p-3 bg-gray-50 rounded-lg">
                      <IonIcon 
                        icon={stat.icon} 
                        className={`text-2xl mb-2 text-${stat.color}`}
                      />
                      <div className="font-bold text-lg">{stat.value}</div>
                      <div className="text-sm text-gray-600">{stat.title}</div>
                      {stat.trend && (
                        <div className="text-xs text-green-600 font-medium">
                          {stat.trend} vs yesterday
                        </div>
                      )}
                    </div>
                  </IonCol>
                ))}
              </IonRow>
            </IonGrid>
          </Card>

          <IonGrid>
            <IonRow>
              {/* Payment Methods */}
              <IonCol size="12" sizeMd="6">
                <Card title="Payment Methods">
                  <div className="space-y-3">
                    {getPaymentMethodStats().map((method, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div className="flex items-center">
                          <IonIcon 
                            icon={method.icon} 
                            className={`mr-3 text-lg text-${method.color}`}
                          />
                          <span className="font-medium">{method.method}</span>
                        </div>
                        <div className="text-right">
                          <div className="font-semibold">
                            ${method.amount.toFixed(2)}
                          </div>
                          <div className="text-sm text-gray-500">
                            {method.percentage.toFixed(1)}%
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </Card>
              </IonCol>

              {/* Recent Transactions */}
              <IonCol size="12" sizeMd="6">
                <Card title="Recent Transactions">
                  {recentTransactions.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      <IonIcon icon={receipt} className="text-3xl mb-2" />
                      <p>No transactions today</p>
                    </div>
                  ) : (
                    <IonList>
                      {recentTransactions.slice(0, 5).map((transaction, index) => (
                        <IonItem key={index} className="px-0">
                          <div className="flex items-center justify-between w-full">
                            <div className="flex items-center">
                              <IonIcon 
                                icon={transaction.paymentMethod === 'cash' ? cash : card} 
                                className="mr-3 text-gray-400"
                              />
                              <div>
                                <div className="font-medium">
                                  #{transaction.receiptNumber}
                                </div>
                                <div className="text-sm text-gray-500">
                                  {new Date(transaction.timestamp).toLocaleTimeString()}
                                </div>
                                {transaction.customerName && (
                                  <div className="text-xs text-gray-400">
                                    {transaction.customerName}
                                  </div>
                                )}
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="font-semibold">
                                ${transaction.total.toFixed(2)}
                              </div>
                              <div className="text-xs text-gray-500 capitalize">
                                {transaction.paymentMethod}
                              </div>
                            </div>
                          </div>
                        </IonItem>
                      ))}
                    </IonList>
                  )}
                  
                  {recentTransactions.length > 5 && (
                    <IonButton
                      fill="clear"
                      expand="block"
                      onClick={handleViewReports}
                      className="mt-3"
                    >
                      View All Transactions
                      <IonIcon icon={chevronForward} slot="end" />
                    </IonButton>
                  )}
                </Card>
              </IonCol>
            </IonRow>
          </IonGrid>

          {/* Top Products */}
          {salesReport && salesReport.topProducts.length > 0 && (
            <Card title="Top Products" className="mb-4">
              <div className="space-y-3">
                {salesReport.topProducts.slice(0, 5).map((product, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center">
                      <IonBadge 
                        color={index === 0 ? 'warning' : index === 1 ? 'medium' : 'light'}
                        className="mr-3"
                      >
                        #{index + 1}
                      </IonBadge>
                      <div>
                        <div className="font-medium">{product.name}</div>
                        <div className="text-sm text-gray-500">
                          {product.quantitySold} sold
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold">
                        ${product.revenue.toFixed(2)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          )}

          {/* Sync Status */}
          {(syncStatus.pendingCount > 0 || !isOnline) && (
            <Card className="mb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <IonIcon 
                    icon={isOnline ? sync : wifiOff} 
                    className={`mr-3 ${isOnline ? 'text-warning' : 'text-danger'}`}
                  />
                  <div>
                    <div className="font-medium">
                      {isOnline ? 'Sync Pending' : 'Offline Mode'}
                    </div>
                    <div className="text-sm text-gray-500">
                      {isOnline 
                        ? `${syncStatus.pendingCount} items pending sync`
                        : 'Data will sync when online'
                      }
                    </div>
                  </div>
                </div>
                {isOnline && (
                  <IonButton
                    fill="outline"
                    size="small"
                    onClick={() => forceSync()}
                    disabled={isSyncing}
                  >
                    {isSyncing ? 'Syncing...' : 'Sync Now'}
                  </IonButton>
                )}
              </div>
              {isSyncing && (
                <IonProgressBar type="indeterminate" className="mt-3" />
              )}
            </Card>
          )}
        </div>

        {/* Toast */}
        <IonToast
          isOpen={showToast}
          onDidDismiss={() => setShowToast(false)}
          message={toastMessage}
          duration={3000}
          position="top"
        />
      </IonContent>
    </IonPage>
  );
};

export default DashboardPage;