import React, { useState, useEffect, useRef } from 'react';
import {
  IonContent,
  IonHeader,
  IonPage,
  IonTitle,
  IonToolbar,
  IonMenuButton,
  IonButton,
  IonIcon,
  IonCard,
  IonCardContent,
  IonText,
  IonItem,
  IonLabel,
  IonNote,
  IonToast,
  IonAlert,
  IonFab,
  IonFabButton,
  IonFabList,
  IonLoading,
} from '@ionic/react';
import {
  print,
  share,
  download,
  checkmarkCircle,
  home,
  receipt,
  business,
  time,
  person,
  card,
  cash,
  phone,
  arrowBack,
  copyOutline,
  mailOutline,
} from 'ionicons/icons';
import { useEnhancedAuth } from '@/contexts/EnhancedAuthContext';
import { posService, POSOrderResponse } from '@/services/posService';
import { POSTransaction, POSReceipt, POSPayment, OrderItem } from '@/types/order';
import { Share } from '@capacitor/share';
import { Filesystem, Directory, Encoding } from '@capacitor/filesystem';
import { isPlatform } from '@ionic/react';

interface ReceiptProps {
  transaction: POSOrderResponse;
  paymentMethod: string;
  cashReceived?: number;
  customerName?: string;
  customerPhone?: string;
  onNewOrder: () => void;
  onDashboard: () => void;
}

const Receipt: React.FC<ReceiptProps> = ({
  transaction,
  paymentMethod,
  cashReceived,
  customerName,
  customerPhone,
  onNewOrder,
  onDashboard,
}) => {
  const { currentUser, company, location } = useEnhancedAuth();
  
  // State
  const [receiptData, setReceiptData] = useState<POSReceipt | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [showShareAlert, setShowShareAlert] = useState(false);
  const [isPrinting, setIsPrinting] = useState(false);
  
  const receiptRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    loadReceiptData();
  }, [transaction]);

  const loadReceiptData = async () => {
    try {
      setIsLoading(true);
      
      // Get full transaction details
      const fullTransaction = await posService.getTransactionById(transaction.orderId);
      
      if (!fullTransaction || !company || !location) {
        throw new Error('Transaction or location data not found');
      }

      // Create receipt data
      const receipt: POSReceipt = {
        transaction: {
          ...fullTransaction,
          receiptNumber: transaction.receiptNumber,
        },
        payment: {
          method: paymentMethod as any,
          amount: fullTransaction.total,
          amountPaid: cashReceived,
          change: cashReceived ? posService.calculateChange(fullTransaction.total, cashReceived) : 0,
          timestamp: receiptTransaction.timestamp,
        },
        companyInfo: {
          name: company.name,
          address: 'Company Address', // You might want to add this to company data
          phone: 'Company Phone', // You might want to add this to company data
          email: 'Company Email', // You might want to add this to company data
        },
        locationInfo: {
          name: location.name,
          address: location.address,
        },
        printedAt: new Date().toISOString(),
        reprintCount: 0,
      };

      setReceiptData(receipt);
    } catch (error) {
      console.error('Failed to load receipt data:', error);
      showToastMessage('Failed to load receipt data');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePrint = async () => {
    if (!receiptData) return;

    try {
      setIsPrinting(true);
      
      if (isPlatform('capacitor')) {
        // Use native print if available
        const receiptHtml = generateReceiptHtml();
        
        // For now, we'll show a message. In a real app, you'd integrate with a printer plugin
        showToastMessage('Print functionality would be implemented with a thermal printer plugin');
      } else {
        // Use browser print
        const printWindow = window.open('', '_blank');
        if (printWindow) {
          printWindow.document.write(generateReceiptHtml());
          printWindow.document.close();
          printWindow.print();
        }
      }
    } catch (error) {
      console.error('Print failed:', error);
      showToastMessage('Failed to print receipt');
    } finally {
      setIsPrinting(false);
    }
  };

  const handleShare = async () => {
    if (!receiptData) return;

    try {
      const receiptText = generateReceiptText();
      
      if (isPlatform('capacitor')) {
        await Share.share({
          title: `Receipt ${receiptData.receiptTransaction.receiptNumber}`,
          text: receiptText,
          dialogTitle: 'Share Receipt',
        });
      } else {
        setShowShareAlert(true);
      }
    } catch (error) {
      console.error('Share failed:', error);
      showToastMessage('Failed to share receipt');
    }
  };

  const handleCopyToClipboard = async () => {
    if (!receiptData) return;

    try {
      const receiptText = generateReceiptText();
      await navigator.clipboard.writeText(receiptText);
      showToastMessage('Receipt copied to clipboard');
    } catch (error) {
      console.error('Copy failed:', error);
      showToastMessage('Failed to copy receipt');
    }
  };

  const handleDownload = async () => {
    if (!receiptData) return;

    try {
      const receiptText = generateReceiptText();
      const fileName = `receipt-${receiptData.receiptTransaction.receiptNumber}.txt`;
      
      if (isPlatform('capacitor')) {
        // Save to device
        await Filesystem.writeFile({
          path: fileName,
          data: receiptText,
          directory: Directory.Documents,
          encoding: Encoding.UTF8,
        });
        showToastMessage(`Receipt saved to Documents/${fileName}`);
      } else {
        // Download in browser
        const blob = new Blob([receiptText], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = fileName;
        a.click();
        URL.revokeObjectURL(url);
        showToastMessage('Receipt downloaded');
      }
    } catch (error) {
      console.error('Download failed:', error);
      showToastMessage('Failed to download receipt');
    }
  };

  const generateReceiptText = (): string => {
    if (!receiptData) return '';

    const { transaction: receiptTransaction, payment, companyInfo, locationInfo } = receiptData;
    const date = new Date(receiptTransaction.timestamp);
    
    return `
================================
${companyInfo.name.toUpperCase()}
================================
${locationInfo.name}
${locationInfo.address}

Receipt #: ${transaction.receiptNumber}
Date: ${date.toLocaleDateString()}
Time: ${date.toLocaleTimeString()}
Cashier: ${transaction.cashierName || currentUser?.name}

${customerName ? `Customer: ${customerName}` : ''}
${customerPhone ? `Phone: ${customerPhone}` : ''}
${transaction.tableNumber ? `Table: ${transaction.tableNumber}` : ''}

================================
ITEMS
================================
${transaction.items.map(item => 
  `${item.quantity}x ${item.name.padEnd(20)} $${(item.price * item.quantity).toFixed(2)}`
).join('\n')}

================================
SUMMARY
================================
Subtotal:        $${transaction.subtotal.toFixed(2)}
Tax:             $${transaction.tax.toFixed(2)}
--------------------------------
TOTAL:           $${transaction.total.toFixed(2)}

Payment Method:  ${payment.method.toUpperCase()}
${payment.method === 'cash' && payment.amountPaid ? `
Amount Paid:     $${payment.amountPaid.toFixed(2)}
Change:          $${(payment.change || 0).toFixed(2)}` : ''}

${transaction.specialInstructions ? `
Special Instructions:
${transaction.specialInstructions}` : ''}

Thank you for your business!
Please come again!

Powered by FoodPrepAI POS
================================
    `.trim();
  };

  const generateReceiptHtml = (): string => {
    if (!receiptData) return '';

    return `
<!DOCTYPE html>
<html>
<head>
    <title>Receipt ${receiptData.receiptTransaction.receiptNumber}</title>
    <style>
        body { 
            font-family: 'Courier New', monospace; 
            font-size: 12px; 
            line-height: 1.4; 
            max-width: 300px; 
            margin: 0 auto; 
            padding: 10px;
        }
        .center { text-align: center; }
        .bold { font-weight: bold; }
        .separator { border-top: 1px dashed #000; margin: 5px 0; }
        .item-line { display: flex; justify-content: space-between; }
        @media print {
            body { margin: 0; padding: 5px; }
        }
    </style>
</head>
<body>
    ${generateReceiptText().replace(/\n/g, '<br>').replace(/=/g, '-')}
</body>
</html>
    `;
  };

  const showToastMessage = (message: string) => {
    setToastMessage(message);
    setShowToast(true);
  };

  if (isLoading) {
    return (
      <IonPage>
        <IonContent>
          <IonLoading isOpen={true} message="Loading receipt..." />
        </IonContent>
      </IonPage>
    );
  }

  if (!receiptData) {
    return (
      <IonPage>
        <IonHeader>
          <IonToolbar color="primary">
            <IonMenuButton slot="start" />
            <IonTitle>Receipt Error</IonTitle>
          </IonToolbar>
        </IonHeader>
        <IonContent>
          <div className="flex flex-col items-center justify-center h-full p-8">
            <IonIcon icon={receipt} className="text-6xl text-gray-400 mb-4" />
            <h2 className="text-xl font-semibold mb-2">Receipt Not Found</h2>
            <p className="text-gray-600 text-center mb-6">
              Unable to load receipt data. Please try again.
            </p>
            <IonButton onClick={onDashboard}>
              Return to Dashboard
            </IonButton>
          </div>
        </IonContent>
      </IonPage>
    );
  }

  const { transaction: receiptTransaction, payment, companyInfo, locationInfo } = receiptData;
  const date = new Date(receiptTransaction.timestamp);

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar color="primary">
          <IonMenuButton slot="start" />
          <IonTitle>Receipt</IonTitle>
          <IonButton fill="clear" slot="end" onClick={onDashboard}>
            <IonIcon icon={home} />
          </IonButton>
        </IonToolbar>
      </IonHeader>

      <IonContent>
        <div className="max-w-md mx-auto p-4">
          {/* Success indicator */}
          <div className="text-center mb-6">
            <IonIcon icon={checkmarkCircle} className="text-6xl text-green-500 mb-2" />
            <h2 className="text-xl font-semibold text-green-700">Payment Successful!</h2>
            <p className="text-gray-600">Transaction completed successfully</p>
          </div>

          {/* Receipt card */}
          <IonCard ref={receiptRef} className="receipt-card">
            <IonCardContent className="font-mono text-sm">
              {/* Header */}
              <div className="text-center mb-4">
                <h1 className="text-lg font-bold uppercase">{companyInfo.name}</h1>
                <p className="text-sm">{locationInfo.name}</p>
                <p className="text-sm">{locationInfo.address}</p>
              </div>

              <hr className="my-4" />

              {/* Transaction info */}
              <div className="space-y-1 mb-4">
                <div className="flex justify-between">
                  <span>Receipt #:</span>
                  <span className="font-semibold">{transaction.receiptNumber}</span>
                </div>
                <div className="flex justify-between">
                  <span>Date:</span>
                  <span>{date.toLocaleDateString()}</span>
                </div>
                <div className="flex justify-between">
                  <span>Time:</span>
                  <span>{date.toLocaleTimeString()}</span>
                </div>
                <div className="flex justify-between">
                  <span>Cashier:</span>
                  <span>{transaction.cashierName || currentUser?.name}</span>
                </div>
                
                {/* Customer info */}
                {customerName && (
                  <div className="flex justify-between">
                    <span>Customer:</span>
                    <span>{customerName}</span>
                  </div>
                )}
                {customerPhone && (
                  <div className="flex justify-between">
                    <span>Phone:</span>
                    <span>{customerPhone}</span>
                  </div>
                )}
                {receiptTransaction.tableNumber && (
                  <div className="flex justify-between">
                    <span>Table:</span>
                    <span>#{transaction.tableNumber}</span>
                  </div>
                )}
              </div>

              <hr className="my-4" />

              {/* Items */}
              <div className="mb-4">
                <h3 className="font-semibold mb-2 uppercase">Items</h3>
                {receiptTransaction.items.map((item, index) => (
                  <div key={index} className="mb-2">
                    <div className="flex justify-between">
                      <span>{item.quantity}x {item.name}</span>
                      <span>${(item.price * item.quantity).toFixed(2)}</span>
                    </div>
                    {item.modifiers && item.modifiers.length > 0 && (
                      <div className="ml-4 text-xs text-gray-600">
                        {item.modifiers.map((mod, modIndex) => (
                          <div key={modIndex}>+ {mod.name} (+${mod.price.toFixed(2)})</div>
                        ))}
                      </div>
                    )}
                    {item.specialInstructions && (
                      <div className="ml-4 text-xs text-gray-600 italic">
                        Note: {item.specialInstructions}
                      </div>
                    )}
                  </div>
                ))}
              </div>

              <hr className="my-4" />

              {/* Summary */}
              <div className="space-y-1 mb-4">
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <span>${transaction.subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Tax:</span>
                  <span>${transaction.tax.toFixed(2)}</span>
                </div>
                <hr className="my-2" />
                <div className="flex justify-between text-lg font-bold">
                  <span>TOTAL:</span>
                  <span>${transaction.total.toFixed(2)}</span>
                </div>
              </div>

              <hr className="my-4" />

              {/* Payment details */}
              <div className="space-y-1 mb-4">
                <div className="flex justify-between">
                  <span>Payment Method:</span>
                  <span className="uppercase">{payment.method}</span>
                </div>
                {payment.method === 'cash' && payment.amountPaid && (
                  <>
                    <div className="flex justify-between">
                      <span>Amount Paid:</span>
                      <span>${payment.amountPaid.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Change:</span>
                      <span>${(payment.change || 0).toFixed(2)}</span>
                    </div>
                  </>
                )}
              </div>

              {/* Special instructions */}
              {receiptTransaction.specialInstructions && (
                <>
                  <hr className="my-4" />
                  <div className="mb-4">
                    <h4 className="font-semibold mb-1">Special Instructions:</h4>
                    <p className="text-sm">{transaction.specialInstructions}</p>
                  </div>
                </>
              )}

              <hr className="my-4" />

              {/* Footer */}
              <div className="text-center text-xs">
                <p className="mb-1">Thank you for your business!</p>
                <p className="mb-2">Please come again!</p>
                <p className="text-gray-500">Powered by FoodPrepAI POS</p>
              </div>
            </IonCardContent>
          </IonCard>

          {/* Action buttons */}
          <div className="grid grid-cols-2 gap-4 mt-6">
            <IonButton
              expand="block"
              fill="outline"
              onClick={onNewOrder}
            >
              <IonIcon icon={receipt} slot="start" />
              New Order
            </IonButton>
            
            <IonButton
              expand="block"
              onClick={onDashboard}
            >
              <IonIcon icon={home} slot="start" />
              Dashboard
            </IonButton>
          </div>
        </div>

        {/* Floating action buttons */}
        <IonFab vertical="bottom" horizontal="end" slot="fixed">
          <IonFabButton>
            <IonIcon icon={share} />
          </IonFabButton>
          <IonFabList side="top">
            <IonFabButton onClick={handlePrint} color="primary">
              <IonIcon icon={print} />
            </IonFabButton>
            <IonFabButton onClick={handleShare} color="secondary">
              <IonIcon icon={mailOutline} />
            </IonFabButton>
            <IonFabButton onClick={handleCopyToClipboard} color="tertiary">
              <IonIcon icon={copyOutline} />
            </IonFabButton>
            <IonFabButton onClick={handleDownload} color="success">
              <IonIcon icon={download} />
            </IonFabButton>
          </IonFabList>
        </IonFab>

        {/* Loading overlay */}
        <IonLoading
          isOpen={isPrinting}
          message="Printing receipt..."
          spinner="crescent"
        />

        {/* Share alert */}
        <IonAlert
          isOpen={showShareAlert}
          onDidDismiss={() => setShowShareAlert(false)}
          header="Share Receipt"
          message="Copy the receipt text to share it:"
          inputs={[
            {
              type: 'textarea',
              value: generateReceiptText(),
              attributes: {
                readonly: true,
                rows: 10,
              },
            },
          ]}
          buttons={[
            'Close',
            {
              text: 'Copy',
              handler: handleCopyToClipboard,
            },
          ]}
        />

        {/* Toast */}
        <IonToast
          isOpen={showToast}
          onDidDismiss={() => setShowToast(false)}
          message={toastMessage}
          duration={3000}
          position="top"
        />
      </IonContent>
    </IonPage>
  );
};

export default Receipt;