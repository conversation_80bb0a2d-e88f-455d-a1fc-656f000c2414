import React, { useState, useEffect } from 'react';
import {
  IonContent,
  IonHeader,
  IonPage,
  IonTitle,
  IonToolbar,
  IonButton,
  IonInput,
  IonItem,
  IonLabel,
  IonSelect,
  IonSelectOption,
  IonText,
  IonSpinner,
  IonIcon,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonGrid,
  IonRow,
  IonCol,
  IonToast
} from '@ionic/react';
import { business, location, keypad, wifi, wifiOff, sync } from 'ionicons/icons';
import { useEnhancedAuth } from '../../contexts/EnhancedAuthContext';
import { Company, Location } from '../../types/api';
import './LoginPage.css';

const LoginPage: React.FC = () => {
  const {
    company,
    location,
    companies,
    locations,
    isOnline,
    isSyncing,
    syncStatus,
    setCompany,
    setLocation,
    loginWithPin,
    refreshCompanies,
    refreshLocations
  } = useEnhancedAuth();

  // Local state
  const [pin, setPin] = useState('');
  const [selectedCompany, setSelectedCompany] = useState<Company | null>(company);
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(location);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [step, setStep] = useState<'company' | 'location' | 'pin'>('company');

  // Load companies on mount
  useEffect(() => {
    if (companies.length === 0 && isOnline) {
      loadCompanies();
    } else if (companies.length > 0 && !selectedCompany) {
      // Auto-select if only one company
      if (companies.length === 1) {
        handleCompanySelect(companies[0]);
      }
    }
  }, [companies, isOnline]);

  // Handle company selection effect
  useEffect(() => {
    if (selectedCompany && locations.length === 0 && isOnline) {
      loadLocations(selectedCompany.id);
    } else if (selectedCompany && locations.length > 0 && !selectedLocation) {
      // Auto-select if only one location
      if (locations.length === 1) {
        handleLocationSelect(locations[0]);
      }
    }
  }, [selectedCompany, locations, isOnline]);

  // Determine current step
  useEffect(() => {
    if (!selectedCompany) {
      setStep('company');
    } else if (!selectedLocation) {
      setStep('location');
    } else {
      setStep('pin');
    }
  }, [selectedCompany, selectedLocation]);

  /**
   * Load companies from server
   */
  const loadCompanies = async () => {
    if (!isOnline) {
      showToastMessage('Cannot load companies while offline');
      return;
    }

    try {
      setIsLoading(true);
      await refreshCompanies();
    } catch (error) {
      console.error('Failed to load companies:', error);
      setError('Failed to load companies. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Load locations for selected company
   */
  const loadLocations = async (companyId: string) => {
    if (!isOnline) {
      showToastMessage('Cannot load locations while offline');
      return;
    }

    try {
      setIsLoading(true);
      await refreshLocations(companyId);
    } catch (error) {
      console.error('Failed to load locations:', error);
      setError('Failed to load locations. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Handle company selection
   */
  const handleCompanySelect = async (company: Company) => {
    try {
      setSelectedCompany(company);
      setSelectedLocation(null); // Reset location when company changes
      await setCompany(company);
      
      // Load locations for this company
      if (isOnline) {
        await loadLocations(company.id);
      }
    } catch (error) {
      console.error('Failed to select company:', error);
      setError('Failed to select company. Please try again.');
    }
  };

  /**
   * Handle location selection
   */
  const handleLocationSelect = async (location: Location) => {
    try {
      setSelectedLocation(location);
      await setLocation(location);
    } catch (error) {
      console.error('Failed to select location:', error);
      setError('Failed to select location. Please try again.');
    }
  };

  /**
   * Handle PIN input
   */
  const handlePinChange = (value: string) => {
    // Only allow numbers and limit to reasonable length
    const numericValue = value.replace(/[^0-9]/g, '').substring(0, 8);
    setPin(numericValue);
  };

  /**
   * Handle login submission
   */
  const handleLogin = async () => {
    if (!pin || !selectedCompany || !selectedLocation) {
      setError('Please enter your PIN');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      
      await loginWithPin(pin);
      
      // Login successful - navigation will be handled by App.tsx
      showToastMessage('Login successful!');
      
    } catch (error: any) {
      console.error('Login failed:', error);
      setError(error.message || 'Login failed. Please check your PIN and try again.');
      setPin(''); // Clear PIN on error
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Show toast message
   */
  const showToastMessage = (message: string) => {
    setToastMessage(message);
    setShowToast(true);
  };

  /**
   * Handle PIN keypad input
   */
  const handleKeypadInput = (digit: string) => {
    if (pin.length < 8) {
      setPin(prev => prev + digit);
    }
  };

  /**
   * Clear PIN
   */
  const clearPin = () => {
    setPin('');
  };

  /**
   * Delete last PIN digit
   */
  const deleteLastDigit = () => {
    setPin(prev => prev.slice(0, -1));
  };

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar>
          <IonTitle>FoodPrepAI POS</IonTitle>
          <IonButton 
            slot="end" 
            fill="clear" 
            size="small"
            color={isOnline ? 'success' : 'danger'}
          >
            <IonIcon icon={isOnline ? wifi : wifiOff} />
            {isSyncing && <IonIcon icon={sync} className="ml-1 animate-spin" />}
          </IonButton>
        </IonToolbar>
      </IonHeader>

      <IonContent className="login-content">
        <div className="login-container">
          <div className="login-header">
            <h1>Welcome to FoodPrepAI</h1>
            <p>Point of Sale System</p>
          </div>

          {/* Connection Status */}
          <div className={`connection-status ${isOnline ? 'online' : 'offline'}`}>
            <IonIcon icon={isOnline ? wifi : wifiOff} />
            <span>
              {isOnline ? 'Online' : 'Offline Mode'}
              {isSyncing && ' - Syncing...'}
            </span>
            {syncStatus.pendingCount > 0 && (
              <span className="pending-sync">
                ({syncStatus.pendingCount} pending)
              </span>
            )}
          </div>

          {/* Company Selection */}
          {step === 'company' && (
            <IonCard>
              <IonCardHeader>
                <IonCardTitle>
                  <IonIcon icon={business} className="mr-2" />
                  Select Company
                </IonCardTitle>
              </IonCardHeader>
              <IonCardContent>
                {companies.length > 0 ? (
                  <div className="company-grid">
                    {companies.map((comp) => (
                      <IonButton
                        key={comp.id}
                        fill="outline"
                        expand="block"
                        className="company-button"
                        onClick={() => handleCompanySelect(comp)}
                        disabled={isLoading}
                      >
                        {comp.name}
                      </IonButton>
                    ))}
                  </div>
                ) : (
                  <div className="empty-state">
                    {isOnline ? (
                      <div>
                        <p>No companies available</p>
                        <IonButton onClick={loadCompanies} disabled={isLoading}>
                          {isLoading ? <IonSpinner /> : 'Retry'}
                        </IonButton>
                      </div>
                    ) : (
                      <p>Please connect to the internet to load companies</p>
                    )}
                  </div>
                )}
              </IonCardContent>
            </IonCard>
          )}

          {/* Location Selection */}
          {step === 'location' && (
            <IonCard>
              <IonCardHeader>
                <IonCardTitle>
                  <IonIcon icon={location} className="mr-2" />
                  Select Location
                  {selectedCompany && (
                    <div className="selected-company">
                      {selectedCompany.name}
                    </div>
                  )}
                </IonCardTitle>
              </IonCardHeader>
              <IonCardContent>
                <IonButton
                  fill="clear"
                  size="small"
                  onClick={() => setSelectedCompany(null)}
                  className="change-company-btn"
                >
                  Change Company
                </IonButton>
                
                {locations.length > 0 ? (
                  <div className="location-grid">
                    {locations.map((loc) => (
                      <IonButton
                        key={loc.id}
                        fill="outline"
                        expand="block"
                        className="location-button"
                        onClick={() => handleLocationSelect(loc)}
                        disabled={isLoading}
                      >
                        <div>
                          <div className="location-name">{loc.name}</div>
                          <div className="location-address">{loc.address}</div>
                        </div>
                      </IonButton>
                    ))}
                  </div>
                ) : (
                  <div className="empty-state">
                    {isOnline ? (
                      <div>
                        <p>No locations available for this company</p>
                        <IonButton 
                          onClick={() => selectedCompany && loadLocations(selectedCompany.id)} 
                          disabled={isLoading}
                        >
                          {isLoading ? <IonSpinner /> : 'Retry'}
                        </IonButton>
                      </div>
                    ) : (
                      <p>Please connect to the internet to load locations</p>
                    )}
                  </div>
                )}
              </IonCardContent>
            </IonCard>
          )}

          {/* PIN Entry */}
          {step === 'pin' && (
            <IonCard>
              <IonCardHeader>
                <IonCardTitle>
                  <IonIcon icon={keypad} className="mr-2" />
                  Enter PIN
                  <div className="selected-info">
                    <div>{selectedCompany?.name}</div>
                    <div>{selectedLocation?.name}</div>
                  </div>
                </IonCardTitle>
              </IonCardHeader>
              <IonCardContent>
                <IonButton
                  fill="clear"
                  size="small"
                  onClick={() => setSelectedLocation(null)}
                  className="change-location-btn"
                >
                  Change Location
                </IonButton>

                {/* PIN Display */}
                <div className="pin-display">
                  <div className="pin-dots">
                    {Array.from({ length: 8 }, (_, i) => (
                      <div
                        key={i}
                        className={`pin-dot ${i < pin.length ? 'filled' : ''}`}
                      />
                    ))}
                  </div>
                </div>

                {/* Virtual Keypad */}
                <div className="keypad">
                  <IonGrid>
                    <IonRow>
                      {[1, 2, 3].map(digit => (
                        <IonCol key={digit} size="4">
                          <IonButton
                            expand="block"
                            fill="outline"
                            onClick={() => handleKeypadInput(digit.toString())}
                            disabled={pin.length >= 8}
                          >
                            {digit}
                          </IonButton>
                        </IonCol>
                      ))}
                    </IonRow>
                    <IonRow>
                      {[4, 5, 6].map(digit => (
                        <IonCol key={digit} size="4">
                          <IonButton
                            expand="block"
                            fill="outline"
                            onClick={() => handleKeypadInput(digit.toString())}
                            disabled={pin.length >= 8}
                          >
                            {digit}
                          </IonButton>
                        </IonCol>
                      ))}
                    </IonRow>
                    <IonRow>
                      {[7, 8, 9].map(digit => (
                        <IonCol key={digit} size="4">
                          <IonButton
                            expand="block"
                            fill="outline"
                            onClick={() => handleKeypadInput(digit.toString())}
                            disabled={pin.length >= 8}
                          >
                            {digit}
                          </IonButton>
                        </IonCol>
                      ))}
                    </IonRow>
                    <IonRow>
                      <IonCol size="4">
                        <IonButton
                          expand="block"
                          fill="clear"
                          onClick={clearPin}
                          disabled={pin.length === 0}
                        >
                          Clear
                        </IonButton>
                      </IonCol>
                      <IonCol size="4">
                        <IonButton
                          expand="block"
                          fill="outline"
                          onClick={() => handleKeypadInput('0')}
                          disabled={pin.length >= 8}
                        >
                          0
                        </IonButton>
                      </IonCol>
                      <IonCol size="4">
                        <IonButton
                          expand="block"
                          fill="clear"
                          onClick={deleteLastDigit}
                          disabled={pin.length === 0}
                        >
                          ⌫
                        </IonButton>
                      </IonCol>
                    </IonRow>
                  </IonGrid>
                </div>

                {/* Login Button */}
                <IonButton
                  expand="block"
                  onClick={handleLogin}
                  disabled={pin.length === 0 || isLoading}
                  className="login-button"
                >
                  {isLoading ? <IonSpinner /> : 'Login'}
                </IonButton>
              </IonCardContent>
            </IonCard>
          )}

          {/* Error Display */}
          {error && (
            <IonText color="danger" className="error-message">
              <p>{error}</p>
            </IonText>
          )}

          {/* Offline Notice */}
          {!isOnline && (
            <div className="offline-notice">
              <IonIcon icon={wifiOff} />
              <span>Working in offline mode. Some features may be limited.</span>
            </div>
          )}
        </div>

        {/* Toast */}
        <IonToast
          isOpen={showToast}
          onDidDismiss={() => setShowToast(false)}
          message={toastMessage}
          duration={3000}
          position="top"
        />
      </IonContent>
    </IonPage>
  );
};

export default LoginPage;