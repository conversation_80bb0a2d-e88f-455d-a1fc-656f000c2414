import React, { useState } from 'react';
import {
  IonContent,
  IonHeader,
  IonPage,
  IonTitle,
  IonToolbar,
  IonMenuButton,
  IonCard,
  IonCardContent,
  IonBadge,
  IonButton,
  IonIcon,
  IonList,
  IonItem,
  IonLabel,
  IonGrid,
  IonRow,
  IonCol,
  IonSegment,
  IonSegmentButton,
} from '@ionic/react';
import { time, checkmark, close, eye } from 'ionicons/icons';
import { Order, OrderStatus } from '@/types/order';

const OrderingPage: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState<'active' | 'completed'>('active');
  
  // Mock orders data - in real app this would come from API
  const [orders] = useState<Order[]>([
    {
      id: 'ORD-001',
      customerName: '<PERSON>',
      items: [
        { id: '1', name: 'Burger', price: 12.99, quantity: 2 },
        { id: '2', name: 'Fries', price: 4.99, quantity: 1 },
      ],
      total: 30.97,
      status: 'preparing',
      orderTime: new Date('2024-01-16T14:30:00'),
      estimatedCompletion: new Date('2024-01-16T15:00:00'),
      orderType: 'dine-in',
      tableNumber: 5,
    },
    {
      id: 'ORD-002',
      customerName: '<PERSON> <PERSON>',
      items: [
        { id: '3', name: 'Pizza', price: 15.99, quantity: 1 },
        { id: '4', name: 'Salad', price: 8.99, quantity: 1 },
      ],
      total: 24.98,
      status: 'ready',
      orderTime: new Date('2024-01-16T14:15:00'),
      estimatedCompletion: new Date('2024-01-16T14:45:00'),
      orderType: 'takeout',
    },
    {
      id: 'ORD-003',
      customerName: 'Bob Johnson',
      items: [
        { id: '1', name: 'Burger', price: 12.99, quantity: 1 },
      ],
      total: 12.99,
      status: 'completed',
      orderTime: new Date('2024-01-16T13:30:00'),
      completedTime: new Date('2024-01-16T14:00:00'),
      orderType: 'delivery',
      deliveryAddress: '123 Main St',
    },
  ]);

  const getStatusColor = (status: OrderStatus) => {
    switch (status) {
      case 'pending': return 'warning';
      case 'preparing': return 'primary';
      case 'ready': return 'success';
      case 'completed': return 'medium';
      case 'cancelled': return 'danger';
      default: return 'medium';
    }
  };

  const getStatusText = (status: OrderStatus) => {
    switch (status) {
      case 'pending': return 'Pending';
      case 'preparing': return 'Preparing';
      case 'ready': return 'Ready';
      case 'completed': return 'Completed';
      case 'cancelled': return 'Cancelled';
      default: return status;
    }
  };

  const activeOrders = orders.filter(order => 
    order.status === 'pending' || order.status === 'preparing' || order.status === 'ready'
  );
  
  const completedOrders = orders.filter(order => 
    order.status === 'completed' || order.status === 'cancelled'
  );

  const displayOrders = selectedTab === 'active' ? activeOrders : completedOrders;

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: true 
    });
  };

  const getEstimatedWaitTime = (order: Order) => {
    const now = new Date();
    const diff = order.estimatedCompletion!.getTime() - now.getTime();
    const minutes = Math.max(0, Math.ceil(diff / (1000 * 60)));
    return minutes;
  };

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar color="primary">
          <IonMenuButton slot="start" />
          <IonTitle>Order Management</IonTitle>
        </IonToolbar>
      </IonHeader>
      <IonContent fullscreen>
        <div className="p-4">
          <IonSegment 
            value={selectedTab} 
            onIonChange={(e) => setSelectedTab(e.detail.value as 'active' | 'completed')}
          >
            <IonSegmentButton value="active">
              <IonLabel>Active Orders ({activeOrders.length})</IonLabel>
            </IonSegmentButton>
            <IonSegmentButton value="completed">
              <IonLabel>Completed ({completedOrders.length})</IonLabel>
            </IonSegmentButton>
          </IonSegment>

          <div className="mt-4">
            {displayOrders.length === 0 ? (
              <div className="text-center text-gray-500 py-8">
                <p>No {selectedTab} orders</p>
              </div>
            ) : (
              <IonGrid>
                <IonRow>
                  {displayOrders.map((order) => (
                    <IonCol key={order.id} size="12" sizeMd="6" sizeLg="4">
                      <IonCard>
                        <IonCardContent>
                          <div className="flex justify-between items-start mb-3">
                            <div>
                              <h3 className="font-semibold text-lg">{order.id}</h3>
                              <p className="text-gray-600">{order.customerName}</p>
                            </div>
                            <IonBadge color={getStatusColor(order.status)}>
                              {getStatusText(order.status)}
                            </IonBadge>
                          </div>

                          <div className="space-y-2 mb-4">
                            <div className="flex justify-between items-center">
                              <span className="text-sm text-gray-600">Order Type:</span>
                              <span className="text-sm font-medium capitalize">
                                {order.orderType}
                              </span>
                            </div>

                            {order.tableNumber && (
                              <div className="flex justify-between items-center">
                                <span className="text-sm text-gray-600">Table:</span>
                                <span className="text-sm font-medium">#{order.tableNumber}</span>
                              </div>
                            )}

                            {order.deliveryAddress && (
                              <div className="flex justify-between items-center">
                                <span className="text-sm text-gray-600">Address:</span>
                                <span className="text-sm font-medium">{order.deliveryAddress}</span>
                              </div>
                            )}

                            <div className="flex justify-between items-center">
                              <span className="text-sm text-gray-600">Order Time:</span>
                              <span className="text-sm">{formatTime(order.orderTime)}</span>
                            </div>

                            {order.estimatedCompletion && selectedTab === 'active' && (
                              <div className="flex justify-between items-center">
                                <span className="text-sm text-gray-600">Est. Ready:</span>
                                <span className="text-sm flex items-center">
                                  <IonIcon icon={time} className="mr-1" />
                                  {getEstimatedWaitTime(order)} min
                                </span>
                              </div>
                            )}

                            {order.completedTime && selectedTab === 'completed' && (
                              <div className="flex justify-between items-center">
                                <span className="text-sm text-gray-600">Completed:</span>
                                <span className="text-sm">{formatTime(order.completedTime)}</span>
                              </div>
                            )}

                            <div className="flex justify-between items-center font-medium">
                              <span>Total:</span>
                              <span className="text-primary-600">${order.total.toFixed(2)}</span>
                            </div>
                          </div>

                          <div className="border-t pt-3 mb-3">
                            <h4 className="text-sm font-medium mb-2">Items:</h4>
                            <IonList className="border rounded">
                              {order.items.map((item, index) => (
                                <IonItem key={index} lines={index < order.items.length - 1 ? 'inset' : 'none'}>
                                  <IonLabel>
                                    <h3 className="text-sm">{item.name}</h3>
                                    <p className="text-xs text-gray-600">
                                      ${item.price.toFixed(2)} x {item.quantity}
                                    </p>
                                  </IonLabel>
                                  <span className="text-sm font-medium">
                                    ${(item.price * item.quantity).toFixed(2)}
                                  </span>
                                </IonItem>
                              ))}
                            </IonList>
                          </div>

                          {selectedTab === 'active' && (
                            <div className="flex space-x-2">
                              <IonButton fill="outline" size="small" expand="block">
                                <IonIcon icon={eye} slot="start" />
                                Details
                              </IonButton>
                              {order.status === 'ready' ? (
                                <IonButton fill="solid" color="success" size="small" expand="block">
                                  <IonIcon icon={checkmark} slot="start" />
                                  Complete
                                </IonButton>
                              ) : (
                                <IonButton fill="solid" size="small" expand="block">
                                  Update Status
                                </IonButton>
                              )}
                            </div>
                          )}
                        </IonCardContent>
                      </IonCard>
                    </IonCol>
                  ))}
                </IonRow>
              </IonGrid>
            )}
          </div>
        </div>
      </IonContent>
    </IonPage>
  );
};

export default OrderingPage;