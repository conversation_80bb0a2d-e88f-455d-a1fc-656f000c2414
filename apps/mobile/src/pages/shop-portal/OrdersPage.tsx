import React, { useState, useEffect } from 'react';
import {
  IonContent,
  IonHeader,
  IonPage,
  IonTitle,
  IonToolbar,
  IonButtons,
  IonBackButton,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonList,
  IonItem,
  IonLabel,
  IonIcon,
  IonBadge,
  IonSegment,
  IonSegmentButton,
  IonRefresher,
  IonRefresherContent,
  IonSearchbar,
  IonSpinner,
  IonText,
  IonButton,
  IonModal,
  IonChip,
  IonGrid,
  IonRow,
  IonCol,
  IonToast,
  RefresherEventDetail,
} from '@ionic/react';
import { 
  timeOutline,
  checkmarkCircle,
  hourglass,
  truck,
  alertCircle,
  eyeOutline,
  refreshOutline,
  documentTextOutline,
  calendarOutline,
  cubeOutline,
  closeCircle
} from 'ionicons/icons';
import { useAuth } from '@/contexts/EnhancedAuthContext';
import { shopPortalService, ShopPortalOrder } from '@/services/shopPortalService';
import { format } from 'date-fns';

const OrdersPage: React.FC = () => {
  const { user, companyId, locationId } = useAuth();
  
  const [orders, setOrders] = useState<ShopPortalOrder[]>([]);
  const [filteredOrders, setFilteredOrders] = useState<ShopPortalOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [searchText, setSearchText] = useState<string>('');
  const [selectedOrder, setSelectedOrder] = useState<ShopPortalOrder | null>(null);
  const [showOrderModal, setShowOrderModal] = useState<boolean>(false);
  const [showToast, setShowToast] = useState<boolean>(false);
  const [toastMessage, setToastMessage] = useState<string>('');
  const [toastColor, setToastColor] = useState<'success' | 'warning' | 'danger'>('success');

  useEffect(() => {
    if (companyId && locationId) {
      shopPortalService.updateCredentials(companyId, locationId);
      fetchOrders();
    }
  }, [companyId, locationId]);

  useEffect(() => {
    filterOrders();
  }, [orders, selectedStatus, searchText]);

  const fetchOrders = async () => {
    try {
      setLoading(true);
      const response = await shopPortalService.getOrders();
      
      if (response.success) {
        setOrders(response.data || []);
      } else {
        showNotification('Failed to load orders', 'danger');
      }
    } catch (error) {
      console.error('Error fetching orders:', error);
      showNotification('Error loading orders', 'danger');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async (event: CustomEvent<RefresherEventDetail>) => {
    await fetchOrders();
    event.detail.complete();
  };

  const filterOrders = () => {
    let filtered = orders;

    // Filter by status
    if (selectedStatus !== 'all') {
      filtered = filtered.filter(order => order.status === selectedStatus);
    }

    // Filter by search text
    if (searchText) {
      filtered = filtered.filter(order => 
        order.orderId.toLowerCase().includes(searchText.toLowerCase()) ||
        order.items.some(item => item.name.toLowerCase().includes(searchText.toLowerCase())) ||
        order.notes?.toLowerCase().includes(searchText.toLowerCase())
      );
    }

    setFilteredOrders(filtered);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return hourglass;
      case 'approved':
        return checkmarkCircle;
      case 'dispatched':
        return truck;
      case 'delivered':
        return checkmarkCircle;
      case 'cancelled':
        return closeCircle;
      default:
        return alertCircle;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'warning';
      case 'approved':
        return 'secondary';
      case 'dispatched':
        return 'primary';
      case 'delivered':
        return 'success';
      case 'cancelled':
        return 'danger';
      default:
        return 'medium';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Pending Review';
      case 'approved':
        return 'Approved';
      case 'dispatched':
        return 'Dispatched';
      case 'delivered':
        return 'Delivered';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status;
    }
  };

  const showOrderDetails = (order: ShopPortalOrder) => {
    setSelectedOrder(order);
    setShowOrderModal(true);
  };

  const canCancelOrder = (order: ShopPortalOrder) => {
    return order.status === 'pending' || order.status === 'approved';
  };

  const cancelOrder = async (orderId: string) => {
    try {
      const response = await shopPortalService.cancelOrder(orderId, 'Cancelled by shop');
      
      if (response.success) {
        showNotification('Order cancelled successfully', 'success');
        fetchOrders(); // Refresh the list
        setShowOrderModal(false);
      } else {
        showNotification('Failed to cancel order', 'danger');
      }
    } catch (error) {
      console.error('Error cancelling order:', error);
      showNotification('Error cancelling order', 'danger');
    }
  };

  const showNotification = (message: string, color: 'success' | 'warning' | 'danger') => {
    setToastMessage(message);
    setToastColor(color);
    setShowToast(true);
  };

  const formatDate = (date: Date | string) => {
    return format(new Date(date), 'MMM d, yyyy');
  };

  const formatDateTime = (date: Date | string) => {
    return format(new Date(date), 'MMM d, yyyy h:mm a');
  };

  const statusCounts = {
    all: orders.length,
    pending: orders.filter(o => o.status === 'pending').length,
    approved: orders.filter(o => o.status === 'approved').length,
    dispatched: orders.filter(o => o.status === 'dispatched').length,
    delivered: orders.filter(o => o.status === 'delivered').length,
  };

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar color="primary">
          <IonButtons slot="start">
            <IonBackButton defaultHref="/shop-portal" />
          </IonButtons>
          <IonTitle>Order History</IonTitle>
          <IonButtons slot="end">
            <IonButton onClick={fetchOrders}>
              <IonIcon icon={refreshOutline} />
            </IonButton>
          </IonButtons>
        </IonToolbar>
      </IonHeader>

      <IonContent>
        <IonRefresher slot="fixed" onIonRefresh={handleRefresh}>
          <IonRefresherContent
            pullingIcon={refreshOutline}
            pullingText="Pull to refresh"
            refreshingSpinner="circles"
            refreshingText="Refreshing..."
          />
        </IonRefresher>

        <div className="p-4">
          {/* Search and Filter */}
          <div className="mb-4">
            <IonSearchbar
              value={searchText}
              onIonInput={(e) => setSearchText(e.detail.value!)}
              placeholder="Search orders..."
              showClearButton="focus"
            />

            <IonSegment
              value={selectedStatus}
              onIonChange={(e) => setSelectedStatus(e.detail.value as string)}
              className="mt-2"
            >
              <IonSegmentButton value="all">
                <IonLabel>All ({statusCounts.all})</IonLabel>
              </IonSegmentButton>
              <IonSegmentButton value="pending">
                <IonLabel>Pending ({statusCounts.pending})</IonLabel>
              </IonSegmentButton>
              <IonSegmentButton value="approved">
                <IonLabel>Approved ({statusCounts.approved})</IonLabel>
              </IonSegmentButton>
              <IonSegmentButton value="dispatched">
                <IonLabel>Dispatched ({statusCounts.dispatched})</IonLabel>
              </IonSegmentButton>
              <IonSegmentButton value="delivered">
                <IonLabel>Delivered ({statusCounts.delivered})</IonLabel>
              </IonSegmentButton>
            </IonSegment>
          </div>

          {/* Orders List */}
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <IonSpinner name="crescent" />
            </div>
          ) : filteredOrders.length === 0 ? (
            <div className="text-center py-12">
              <IonIcon icon={documentTextOutline} style={{ fontSize: '64px' }} className="text-gray-400 mb-4" />
              <h3 className="text-lg font-medium">No orders found</h3>
              <p className="text-gray-500">
                {searchText || selectedStatus !== 'all' 
                  ? 'Try adjusting your search or filters' 
                  : 'Start by placing your first order'
                }
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {filteredOrders.map((order) => (
                <IonCard key={order.id} button onClick={() => showOrderDetails(order)}>
                  <IonCardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <IonCardTitle className="text-lg">
                          Order #{order.orderId}
                        </IonCardTitle>
                        <IonText color="medium">
                          <p className="text-sm mt-1">
                            {formatDateTime(order.createdAt)}
                          </p>
                        </IonText>
                      </div>
                      <IonBadge color={getStatusColor(order.status)}>
                        {getStatusText(order.status)}
                      </IonBadge>
                    </div>
                  </IonCardHeader>

                  <IonCardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Items:</span>
                        <span className="font-medium">{order.items.length} items</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Total Cost:</span>
                        <span className="font-medium text-primary">
                          ${order.totalCost.toFixed(2)}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Delivery Date:</span>
                        <span className="font-medium">
                          {formatDate(order.requestedDeliveryDate)}
                        </span>
                      </div>
                      {order.notes && (
                        <div className="mt-2 p-2 bg-gray-50 rounded">
                          <IonText color="medium">
                            <p className="text-sm italic">"{order.notes}"</p>
                          </IonText>
                        </div>
                      )}
                    </div>

                    <div className="flex justify-between items-center mt-4">
                      <div className="flex items-center">
                        <IonIcon 
                          icon={getStatusIcon(order.status)} 
                          color={getStatusColor(order.status)} 
                          className="mr-2"
                        />
                        <IonText color={getStatusColor(order.status)}>
                          <span className="text-sm font-medium">
                            {getStatusText(order.status)}
                          </span>
                        </IonText>
                      </div>
                      <IonButton fill="clear" size="small">
                        <IonIcon icon={eyeOutline} slot="start" />
                        View Details
                      </IonButton>
                    </div>
                  </IonCardContent>
                </IonCard>
              ))}
            </div>
          )}
        </div>

        {/* Order Details Modal */}
        <IonModal isOpen={showOrderModal} onDidDismiss={() => setShowOrderModal(false)}>
          <IonHeader>
            <IonToolbar>
              <IonTitle>Order #{selectedOrder?.orderId}</IonTitle>
              <IonButtons slot="end">
                <IonButton onClick={() => setShowOrderModal(false)}>Close</IonButton>
              </IonButtons>
            </IonToolbar>
          </IonHeader>
          <IonContent>
            {selectedOrder && (
              <div className="p-4">
                {/* Order Status */}
                <IonCard>
                  <IonCardHeader>
                    <IonCardTitle>Order Status</IonCardTitle>
                  </IonCardHeader>
                  <IonCardContent>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <IonIcon 
                          icon={getStatusIcon(selectedOrder.status)} 
                          color={getStatusColor(selectedOrder.status)} 
                          className="text-2xl mr-3"
                        />
                        <div>
                          <IonText color={getStatusColor(selectedOrder.status)}>
                            <h3 className="font-semibold">{getStatusText(selectedOrder.status)}</h3>
                          </IonText>
                          <IonText color="medium">
                            <p className="text-sm">Updated {formatDateTime(selectedOrder.updatedAt)}</p>
                          </IonText>
                        </div>
                      </div>
                      {canCancelOrder(selectedOrder) && (
                        <IonButton 
                          color="danger" 
                          fill="outline" 
                          size="small"
                          onClick={() => cancelOrder(selectedOrder.id!)}
                        >
                          Cancel Order
                        </IonButton>
                      )}
                    </div>
                  </IonCardContent>
                </IonCard>

                {/* Order Details */}
                <IonCard>
                  <IonCardHeader>
                    <IonCardTitle>Order Information</IonCardTitle>
                  </IonCardHeader>
                  <IonCardContent>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Order Date:</span>
                        <span className="font-medium">{formatDateTime(selectedOrder.createdAt)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Requested Delivery:</span>
                        <span className="font-medium">{formatDate(selectedOrder.requestedDeliveryDate)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Total Cost:</span>
                        <span className="font-medium text-primary">${selectedOrder.totalCost.toFixed(2)}</span>
                      </div>
                      {selectedOrder.notes && (
                        <div>
                          <span className="text-gray-600">Notes:</span>
                          <div className="mt-1 p-2 bg-gray-50 rounded">
                            <p className="text-sm">{selectedOrder.notes}</p>
                          </div>
                        </div>
                      )}
                    </div>
                  </IonCardContent>
                </IonCard>

                {/* Order Items */}
                <IonCard>
                  <IonCardHeader>
                    <IonCardTitle>Items Ordered</IonCardTitle>
                  </IonCardHeader>
                  <IonCardContent className="p-0">
                    <IonList>
                      {selectedOrder.items.map((item, index) => (
                        <IonItem key={index} lines={index < selectedOrder.items.length - 1 ? 'full' : 'none'}>
                          <IonLabel>
                            <h3 className="font-medium">{item.name}</h3>
                            <p className="text-sm text-gray-600">
                              {item.quantity} {item.unit} × ${item.estimatedCost.toFixed(2)}
                            </p>
                          </IonLabel>
                          <IonText slot="end" className="font-medium">
                            ${(item.quantity * item.estimatedCost).toFixed(2)}
                          </IonText>
                        </IonItem>
                      ))}
                    </IonList>
                  </IonCardContent>
                </IonCard>
              </div>
            )}
          </IonContent>
        </IonModal>

        <IonToast
          isOpen={showToast}
          onDidDismiss={() => setShowToast(false)}
          message={toastMessage}
          color={toastColor}
          duration={3000}
          position="top"
        />
      </IonContent>
    </IonPage>
  );
};

export default OrdersPage;