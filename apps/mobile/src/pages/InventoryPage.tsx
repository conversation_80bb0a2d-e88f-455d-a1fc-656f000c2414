import React, { useState, useEffect } from 'react';
import {
  IonContent,
  IonHeader,
  IonPage,
  IonTitle,
  IonToolbar,
  IonMenuButton,
  IonCard,
  IonCardContent,
  IonButton,
  IonIcon,
  IonBadge,
  IonGrid,
  IonRow,
  IonCol,
  IonProgressBar,
  IonRefresher,
  IonRefresherContent,
  RefresherEventDetail,
} from '@ionic/react';
import { 
  list, 
  calculator, 
  trash, 
  warning, 
  checkmark, 
  cube,
  time,
  cash,
  trending,
  barChart,
  scan,
} from 'ionicons/icons';
import { InventoryItem } from '@/types/inventory';
import { inventoryService } from '@/services/inventoryService';

const InventoryPage: React.FC = () => {
  const [stats, setStats] = useState({
    totalItems: 0,
    lowStock: 0,
    outOfStock: 0,
    totalValue: 0,
    expiringSoon: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load inventory statistics
  const loadStats = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const [inventoryResponse, lowStockResponse, expiringResponse] = await Promise.all([
        inventoryService.getInventoryItems(),
        inventoryService.getLowStockItems(),
        inventoryService.getExpiringItems(7),
      ]);

      if (inventoryResponse.success && inventoryResponse.data) {
        const items = inventoryResponse.data;
        const totalValue = items.reduce((sum, item) => sum + (item.currentStock * item.costPerUnit), 0);
        const outOfStock = items.filter(item => item.currentStock <= 0).length;

        setStats({
          totalItems: items.length,
          lowStock: lowStockResponse.success ? lowStockResponse.data?.length || 0 : 0,
          outOfStock,
          totalValue,
          expiringSoon: expiringResponse.success ? expiringResponse.data?.length || 0 : 0,
        });
      }
    } catch (err) {
      console.error('Error loading stats:', err);
      setError('Failed to load inventory statistics');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadStats();
  }, []);

  // Handle refresh
  const handleRefresh = async (event: CustomEvent<RefresherEventDetail>) => {
    await loadStats();
    event.detail.complete();
  };

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar color="primary">
          <IonMenuButton slot="start" />
          <IonTitle>Inventory Management</IonTitle>
        </IonToolbar>
      </IonHeader>
      
      <IonContent fullscreen>
        <IonRefresher slot="fixed" onIonRefresh={handleRefresh}>
          <IonRefresherContent></IonRefresherContent>
        </IonRefresher>

        {loading && <IonProgressBar type="indeterminate" />}

        <div className="p-4">
          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
            <IonCard className="stat-card">
              <IonCardContent className="text-center p-4">
                <IonIcon icon={cube} className="text-3xl text-blue-500 mb-2" />
                <div className="text-2xl font-bold">{stats.totalItems}</div>
                <div className="text-sm text-gray-600">Total Items</div>
              </IonCardContent>
            </IonCard>

            <IonCard className="stat-card">
              <IonCardContent className="text-center p-4">
                <IonIcon icon={warning} className="text-3xl text-yellow-500 mb-2" />
                <div className="text-2xl font-bold text-yellow-600">{stats.lowStock}</div>
                <div className="text-sm text-gray-600">Low Stock</div>
              </IonCardContent>
            </IonCard>

            <IonCard className="stat-card">
              <IonCardContent className="text-center p-4">
                <IonIcon icon={time} className="text-3xl text-red-500 mb-2" />
                <div className="text-2xl font-bold text-red-600">{stats.outOfStock}</div>
                <div className="text-sm text-gray-600">Out of Stock</div>
              </IonCardContent>
            </IonCard>

            <IonCard className="stat-card">
              <IonCardContent className="text-center p-4">
                <IonIcon icon={cash} className="text-3xl text-green-500 mb-2" />
                <div className="text-2xl font-bold text-green-600">
                  ${stats.totalValue.toFixed(0)}
                </div>
                <div className="text-sm text-gray-600">Total Value</div>
              </IonCardContent>
            </IonCard>

            <IonCard className="stat-card">
              <IonCardContent className="text-center p-4">
                <IonIcon icon={trending} className="text-3xl text-orange-500 mb-2" />
                <div className="text-2xl font-bold text-orange-600">{stats.expiringSoon}</div>
                <div className="text-sm text-gray-600">Expiring Soon</div>
              </IonCardContent>
            </IonCard>
          </div>

          {/* Alert Section */}
          {(stats.lowStock > 0 || stats.outOfStock > 0 || stats.expiringSoon > 0) && (
            <IonCard className="mb-6 border-l-4 border-red-500">
              <IonCardContent>
                <h3 className="font-semibold text-lg mb-3 flex items-center">
                  <IonIcon icon={warning} className="text-red-500 mr-2" />
                  Attention Required
                </h3>
                <div className="space-y-2">
                  {stats.outOfStock > 0 && (
                    <div className="flex items-center justify-between p-2 bg-red-50 rounded">
                      <span className="text-red-700">{stats.outOfStock} items out of stock</span>
                      <IonBadge color="danger">Critical</IonBadge>
                    </div>
                  )}
                  {stats.lowStock > 0 && (
                    <div className="flex items-center justify-between p-2 bg-yellow-50 rounded">
                      <span className="text-yellow-700">{stats.lowStock} items low in stock</span>
                      <IonBadge color="warning">Warning</IonBadge>
                    </div>
                  )}
                  {stats.expiringSoon > 0 && (
                    <div className="flex items-center justify-between p-2 bg-orange-50 rounded">
                      <span className="text-orange-700">{stats.expiringSoon} items expiring soon</span>
                      <IonBadge color="warning">Check</IonBadge>
                    </div>
                  )}
                </div>
              </IonCardContent>
            </IonCard>
          )}

          {/* Main Actions */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <IonCard className="action-card cursor-pointer" routerLink="/inventory/list">
              <IonCardContent className="p-6">
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                    <IonIcon icon={list} className="text-2xl text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-lg">View Inventory</h3>
                    <p className="text-gray-600 text-sm">Browse and manage all inventory items</p>
                  </div>
                </div>
              </IonCardContent>
            </IonCard>

            <IonCard className="action-card cursor-pointer" routerLink="/inventory/stock-count">
              <IonCardContent className="p-6">
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                    <IonIcon icon={calculator} className="text-2xl text-green-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-lg">Stock Count</h3>
                    <p className="text-gray-600 text-sm">Perform physical inventory counts</p>
                  </div>
                </div>
              </IonCardContent>
            </IonCard>

            <IonCard className="action-card cursor-pointer" routerLink="/inventory/wastage">
              <IonCardContent className="p-6">
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mr-4">
                    <IonIcon icon={trash} className="text-2xl text-red-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-lg">Record Wastage</h3>
                    <p className="text-gray-600 text-sm">Log damaged, expired, or wasted items</p>
                  </div>
                </div>
              </IonCardContent>
            </IonCard>

            <IonCard className="action-card cursor-pointer">
              <IonCardContent className="p-6">
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                    <IonIcon icon={barChart} className="text-2xl text-purple-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-lg">Reports</h3>
                    <p className="text-gray-600 text-sm">View inventory analytics and reports</p>
                  </div>
                </div>
              </IonCardContent>
            </IonCard>
          </div>

          {/* Quick Actions */}
          <IonCard>
            <IonCardContent className="p-6">
              <h3 className="font-semibold text-lg mb-4">Quick Actions</h3>
              <div className="grid grid-cols-2 gap-3">
                <IonButton fill="outline" expand="block">
                  <IonIcon icon={scan} slot="start" />
                  Scan Barcode
                </IonButton>
                <IonButton fill="outline" expand="block">
                  <IonIcon icon={warning} slot="start" />
                  View Alerts
                </IonButton>
              </div>
            </IonCardContent>
          </IonCard>

          {error && (
            <IonCard className="mt-4 border-l-4 border-red-500">
              <IonCardContent>
                <div className="flex items-center">
                  <IonIcon icon={warning} className="text-red-500 mr-2" />
                  <span className="text-red-700">{error}</span>
                </div>
                <IonButton 
                  fill="outline" 
                  size="small" 
                  onClick={loadStats}
                  className="mt-2"
                >
                  Retry
                </IonButton>
              </IonCardContent>
            </IonCard>
          )}
        </div>
      </IonContent>
    </IonPage>
  );
};

export default InventoryPage;