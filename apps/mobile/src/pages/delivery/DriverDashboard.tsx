import React, { useState, useEffect } from 'react';
import {
  IonContent,
  IonHeader,
  IonPage,
  IonTitle,
  IonToolbar,
  IonButtons,
  IonBackButton,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonList,
  IonItem,
  IonLabel,
  IonIcon,
  IonBadge,
  IonButton,
  IonSegment,
  IonSegmentButton,
  IonRefresher,
  IonRefresherContent,
  IonSpinner,
  IonText,
  IonGrid,
  IonRow,
  IonCol,
  IonToast,
  IonModal,
  IonFab,
  IonFabButton,
  RefresherEventDetail,
} from '@ionic/react';
import { 
  truckOutline,
  mapOutline,
  checkmarkCircle,
  timeOutline,
  locationOutline,
  callOutline,
  cameraOutline,
  navigateOutline,
  documentTextOutline,
  refreshOutline,
  alertCircle,
  cubeOutline,
  personOutline,
  playOutline,
  stopOutline
} from 'ionicons/icons';
import { useAuth } from '@/contexts/EnhancedAuthContext';
import { shopPortalService, DeliveryNote } from '@/services/shopPortalService';
import SignatureCapture, { SignatureData } from '@/components/delivery/SignatureCapture';
import { format } from 'date-fns';

interface DeliveryStop {
  id: string;
  deliveryNoteNumber: string;
  orderId: string;
  shopName: string;
  shopAddress: string;
  shopPhone: string;
  estimatedArrival: Date;
  items: Array<{
    itemId: string;
    name: string;
    quantity: number;
    unit: string;
  }>;
  status: 'pending' | 'in-transit' | 'arrived' | 'delivered';
  priority: 'normal' | 'urgent';
  specialInstructions?: string;
}

const DriverDashboard: React.FC = () => {
  const { user, companyId, locationId } = useAuth();
  
  const [deliveries, setDeliveries] = useState<DeliveryStop[]>([]);
  const [selectedTab, setSelectedTab] = useState<'today' | 'completed'>('today');
  const [selectedDelivery, setSelectedDelivery] = useState<DeliveryStop | null>(null);
  const [showDeliveryModal, setShowDeliveryModal] = useState(false);
  const [loading, setLoading] = useState(true);
  const [isOnRoute, setIsOnRoute] = useState(false);
  const [currentLocation, setCurrentLocation] = useState<{latitude: number, longitude: number} | null>(null);
  const [shopSignature, setShopSignature] = useState<SignatureData | null>(null);
  const [deliveryPhotos, setDeliveryPhotos] = useState<string[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastColor, setToastColor] = useState<'success' | 'warning' | 'danger'>('success');

  // Mock driver data - in real app this would come from user profile
  const driverInfo = {
    id: user?.id || 'driver_001',
    name: user?.name || 'John Smith',
    vehicleNumber: 'VH-001',
    phone: '+1234567890'
  };

  useEffect(() => {
    if (companyId) {
      fetchDriverDeliveries();
      startLocationTracking();
    }
  }, [companyId]);

  const fetchDriverDeliveries = async () => {
    try {
      setLoading(true);
      // Mock data - in real app this would call API to get driver's assigned deliveries
      const mockDeliveries: DeliveryStop[] = [
        {
          id: '1',
          deliveryNoteNumber: 'DN-001',
          orderId: 'ORD-123',
          shopName: 'Downtown Cafe',
          shopAddress: '123 Main St, Downtown',
          shopPhone: '+1234567890',
          estimatedArrival: new Date(Date.now() + 30 * 60 * 1000), // 30 mins from now
          items: [
            { itemId: '1', name: 'Coffee Beans', quantity: 5, unit: 'kg' },
            { itemId: '2', name: 'Milk', quantity: 10, unit: 'liters' },
          ],
          status: 'pending',
          priority: 'urgent',
          specialInstructions: 'Please call before delivery'
        },
        {
          id: '2',
          deliveryNoteNumber: 'DN-002',
          orderId: 'ORD-124',
          shopName: 'Westside Restaurant',
          shopAddress: '456 Oak Ave, Westside',
          shopPhone: '+1234567891',
          estimatedArrival: new Date(Date.now() + 90 * 60 * 1000), // 90 mins from now
          items: [
            { itemId: '3', name: 'Fresh Vegetables', quantity: 20, unit: 'kg' },
            { itemId: '4', name: 'Chicken', quantity: 15, unit: 'kg' },
          ],
          status: 'pending',
          priority: 'normal'
        }
      ];
      
      setDeliveries(mockDeliveries);
    } catch (error) {
      console.error('Error fetching deliveries:', error);
      showNotification('Error loading deliveries', 'danger');
    } finally {
      setLoading(false);
    }
  };

  const startLocationTracking = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setCurrentLocation({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude
          });
        },
        (error) => {
          console.warn('Error getting location:', error);
        }
      );
    }
  };

  const handleRefresh = async (event: CustomEvent<RefresherEventDetail>) => {
    await fetchDriverDeliveries();
    event.detail.complete();
  };

  const startRoute = () => {
    setIsOnRoute(true);
    showNotification('Route started - GPS tracking enabled', 'success');
  };

  const endRoute = () => {
    setIsOnRoute(false);
    showNotification('Route ended', 'success');
  };

  const navigateToShop = (delivery: DeliveryStop) => {
    const address = encodeURIComponent(delivery.shopAddress);
    const mapsUrl = `https://maps.google.com/maps?q=${address}`;
    window.open(mapsUrl, '_system');
  };

  const callShop = (delivery: DeliveryStop) => {
    window.open(`tel:${delivery.shopPhone}`, '_system');
  };

  const markAsArrived = (deliveryId: string) => {
    setDeliveries(prev => prev.map(d => 
      d.id === deliveryId ? { ...d, status: 'arrived' } : d
    ));
    showNotification('Marked as arrived', 'success');
  };

  const startDelivery = (delivery: DeliveryStop) => {
    setSelectedDelivery(delivery);
    setShopSignature(null);
    setDeliveryPhotos([]);
    setShowDeliveryModal(true);
  };

  const handleShopSignature = (signature: SignatureData) => {
    setShopSignature(signature);
    showNotification('Shop signature captured', 'success');
  };

  const takeDeliveryPhoto = async () => {
    // In a real app, this would use Capacitor Camera plugin
    // For now, we'll simulate photo capture
    const mockPhotoUrl = `data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=`;
    
    setDeliveryPhotos(prev => [...prev, mockPhotoUrl]);
    showNotification('Photo added', 'success');
  };

  const completeDelivery = async () => {
    if (!selectedDelivery || !shopSignature) {
      showNotification('Shop signature required', 'warning');
      return;
    }

    try {
      setIsProcessing(true);
      
      // In real app, this would call API to update delivery status
      const deliveryCompletionData = {
        deliveryNoteId: selectedDelivery.id,
        driverId: driverInfo.id,
        shopSignature,
        deliveryPhotos,
        completedAt: new Date().toISOString(),
        status: 'delivered'
      };

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Update local state
      setDeliveries(prev => prev.map(d => 
        d.id === selectedDelivery.id ? { ...d, status: 'delivered' } : d
      ));
      
      showNotification('Delivery completed successfully!', 'success');
      setShowDeliveryModal(false);
      
    } catch (error) {
      console.error('Error completing delivery:', error);
      showNotification('Error completing delivery', 'danger');
    } finally {
      setIsProcessing(false);
    }
  };

  const showNotification = (message: string, color: 'success' | 'warning' | 'danger') => {
    setToastMessage(message);
    setToastColor(color);
    setShowToast(true);
  };

  const formatTime = (date: Date) => {
    return format(date, 'h:mm a');
  };

  const formatDateTime = (date: Date) => {
    return format(date, 'MMM d, h:mm a');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'warning';
      case 'in-transit':
        return 'primary';
      case 'arrived':
        return 'secondary';
      case 'delivered':
        return 'success';
      default:
        return 'medium';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Pending';
      case 'in-transit':
        return 'In Transit';
      case 'arrived':
        return 'Arrived';
      case 'delivered':
        return 'Delivered';
      default:
        return status;
    }
  };

  const todayDeliveries = deliveries.filter(d => d.status !== 'delivered');
  const completedDeliveries = deliveries.filter(d => d.status === 'delivered');

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar color="primary">
          <IonButtons slot="start">
            <IonBackButton defaultHref="/delivery" />
          </IonButtons>
          <IonTitle>Driver Dashboard</IonTitle>
          <IonButtons slot="end">
            <IonButton onClick={fetchDriverDeliveries}>
              <IonIcon icon={refreshOutline} />
            </IonButton>
          </IonButtons>
        </IonToolbar>
      </IonHeader>

      <IonContent>
        <IonRefresher slot="fixed" onIonRefresh={handleRefresh}>
          <IonRefresherContent
            pullingIcon={refreshOutline}
            pullingText="Pull to refresh"
            refreshingSpinner="circles"
            refreshingText="Refreshing..."
          />
        </IonRefresher>

        <div className="p-4">
          {/* Driver Info */}
          <IonCard>
            <IonCardContent>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <IonIcon icon={personOutline} className="text-3xl mr-3" color="primary" />
                  <div>
                    <h3 className="font-bold">{driverInfo.name}</h3>
                    <p className="text-sm text-gray-600">Vehicle: {driverInfo.vehicleNumber}</p>
                  </div>
                </div>
                <div className="text-right">
                  <IonBadge color={isOnRoute ? 'success' : 'medium'}>
                    {isOnRoute ? 'On Route' : 'Off Route'}
                  </IonBadge>
                  <p className="text-xs text-gray-600 mt-1">
                    {todayDeliveries.length} pending deliveries
                  </p>
                </div>
              </div>
            </IonCardContent>
          </IonCard>

          {/* Route Controls */}
          <IonCard>
            <IonCardContent>
              <div className="flex space-x-2">
                {!isOnRoute ? (
                  <IonButton expand="block" onClick={startRoute}>
                    <IonIcon icon={playOutline} slot="start" />
                    Start Route
                  </IonButton>
                ) : (
                  <IonButton expand="block" color="danger" onClick={endRoute}>
                    <IonIcon icon={stopOutline} slot="start" />
                    End Route
                  </IonButton>
                )}
              </div>
            </IonCardContent>
          </IonCard>

          {/* Delivery Tabs */}
          <IonSegment
            value={selectedTab}
            onIonChange={(e) => setSelectedTab(e.detail.value as 'today' | 'completed')}
            className="mb-4"
          >
            <IonSegmentButton value="today">
              <IonLabel>Today ({todayDeliveries.length})</IonLabel>
            </IonSegmentButton>
            <IonSegmentButton value="completed">
              <IonLabel>Completed ({completedDeliveries.length})</IonLabel>
            </IonSegmentButton>
          </IonSegment>

          {/* Deliveries List */}
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <IonSpinner name="crescent" />
            </div>
          ) : (
            <div className="space-y-3">
              {(selectedTab === 'today' ? todayDeliveries : completedDeliveries).map((delivery) => (
                <IonCard key={delivery.id}>
                  <IonCardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <IonCardTitle className="text-lg">{delivery.shopName}</IonCardTitle>
                        <IonText color="medium">
                          <p className="text-sm">#{delivery.deliveryNoteNumber}</p>
                        </IonText>
                      </div>
                      <div className="flex flex-col items-end space-y-1">
                        <IonBadge color={getStatusColor(delivery.status)}>
                          {getStatusText(delivery.status)}
                        </IonBadge>
                        {delivery.priority === 'urgent' && (
                          <IonBadge color="danger" size="small">URGENT</IonBadge>
                        )}
                      </div>
                    </div>
                  </IonCardHeader>

                  <IonCardContent>
                    <div className="space-y-2">
                      <div className="flex items-center text-sm">
                        <IonIcon icon={locationOutline} className="mr-2" />
                        <span className="text-gray-600">{delivery.shopAddress}</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <IonIcon icon={timeOutline} className="mr-2" />
                        <span className="text-gray-600">ETA: {formatTime(delivery.estimatedArrival)}</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <IonIcon icon={cubeOutline} className="mr-2" />
                        <span className="text-gray-600">{delivery.items.length} items</span>
                      </div>
                      {delivery.specialInstructions && (
                        <div className="mt-2 p-2 bg-yellow-50 rounded">
                          <IonText color="warning">
                            <p className="text-sm">📋 {delivery.specialInstructions}</p>
                          </IonText>
                        </div>
                      )}
                    </div>

                    {/* Action Buttons */}
                    <div className="mt-4 space-y-2">
                      <IonGrid>
                        <IonRow>
                          <IonCol size="6">
                            <IonButton 
                              fill="outline" 
                              size="small" 
                              expand="block"
                              onClick={() => navigateToShop(delivery)}
                            >
                              <IonIcon icon={navigateOutline} slot="start" />
                              Navigate
                            </IonButton>
                          </IonCol>
                          <IonCol size="6">
                            <IonButton 
                              fill="outline" 
                              size="small" 
                              expand="block"
                              onClick={() => callShop(delivery)}
                            >
                              <IonIcon icon={callOutline} slot="start" />
                              Call
                            </IonButton>
                          </IonCol>
                        </IonRow>
                      </IonGrid>

                      {delivery.status === 'pending' && (
                        <IonButton 
                          expand="block" 
                          onClick={() => markAsArrived(delivery.id)}
                        >
                          <IonIcon icon={checkmarkCircle} slot="start" />
                          Mark as Arrived
                        </IonButton>
                      )}

                      {delivery.status === 'arrived' && (
                        <IonButton 
                          expand="block" 
                          color="success"
                          onClick={() => startDelivery(delivery)}
                        >
                          <IonIcon icon={truckOutline} slot="start" />
                          Complete Delivery
                        </IonButton>
                      )}
                    </div>
                  </IonCardContent>
                </IonCard>
              ))}

              {(selectedTab === 'today' ? todayDeliveries : completedDeliveries).length === 0 && (
                <div className="text-center py-12">
                  <IonIcon icon={documentTextOutline} style={{ fontSize: '64px' }} className="text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium">
                    {selectedTab === 'today' ? 'No pending deliveries' : 'No completed deliveries'}
                  </h3>
                  <p className="text-gray-500">
                    {selectedTab === 'today' ? 'All caught up!' : 'Complete some deliveries to see them here'}
                  </p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Delivery Completion Modal */}
        <IonModal isOpen={showDeliveryModal} onDidDismiss={() => setShowDeliveryModal(false)}>
          <IonHeader>
            <IonToolbar>
              <IonTitle>Complete Delivery</IonTitle>
              <IonButtons slot="end">
                <IonButton onClick={() => setShowDeliveryModal(false)}>Close</IonButton>
              </IonButtons>
            </IonToolbar>
          </IonHeader>
          <IonContent>
            {selectedDelivery && (
              <div className="p-4">
                {/* Delivery Info */}
                <IonCard>
                  <IonCardHeader>
                    <IonCardTitle>{selectedDelivery.shopName}</IonCardTitle>
                  </IonCardHeader>
                  <IonCardContent>
                    <div className="space-y-2 text-sm">
                      <p><strong>Delivery:</strong> #{selectedDelivery.deliveryNoteNumber}</p>
                      <p><strong>Address:</strong> {selectedDelivery.shopAddress}</p>
                      <p><strong>Items:</strong> {selectedDelivery.items.length} items delivered</p>
                    </div>
                  </IonCardContent>
                </IonCard>

                {/* Photo Evidence */}
                <IonCard>
                  <IonCardHeader>
                    <IonCardTitle>Delivery Photos</IonCardTitle>
                  </IonCardHeader>
                  <IonCardContent>
                    <IonButton 
                      expand="block" 
                      fill="outline" 
                      onClick={takeDeliveryPhoto}
                    >
                      <IonIcon icon={cameraOutline} slot="start" />
                      Take Photo ({deliveryPhotos.length})
                    </IonButton>
                    {deliveryPhotos.length > 0 && (
                      <div className="mt-3 text-sm text-green-600">
                        ✓ {deliveryPhotos.length} photo(s) captured
                      </div>
                    )}
                  </IonCardContent>
                </IonCard>

                {/* Shop Signature */}
                <SignatureCapture
                  title="Shop Manager Signature"
                  roleLabel="Shop Manager"
                  onSignatureCapture={handleShopSignature}
                  existingSignature={shopSignature}
                />

                {/* Complete Delivery */}
                <div className="mt-6">
                  <IonButton
                    expand="block"
                    size="large"
                    onClick={completeDelivery}
                    disabled={!shopSignature || isProcessing}
                  >
                    <IonIcon icon={checkmarkCircle} slot="start" />
                    {isProcessing ? 'Processing...' : 'Complete Delivery'}
                  </IonButton>
                </div>
              </div>
            )}
          </IonContent>
        </IonModal>

        <IonToast
          isOpen={showToast}
          onDidDismiss={() => setShowToast(false)}
          message={toastMessage}
          color={toastColor}
          duration={3000}
          position="top"
        />
      </IonContent>
    </IonPage>
  );
};

export default DriverDashboard;