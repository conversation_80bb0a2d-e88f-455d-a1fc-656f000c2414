import React, { useState } from 'react';
import {
  IonList,
  IonItem,
  IonLabel,
  IonRadio,
  IonRadioGroup,
  IonBadge,
  IonIcon,
  IonSelect,
  IonSelectOption,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
} from '@ionic/react';
import { camera, document, warning } from 'ionicons/icons';
import { WastageReason, WASTAGE_REASONS } from '@/types/inventory';

interface WastageReasonSelectorProps {
  selectedReason?: WastageReason | null;
  onReasonChange: (reason: WastageReason) => void;
  variant?: 'list' | 'select' | 'cards';
  className?: string;
}

const WastageReasonSelector: React.FC<WastageReasonSelectorProps> = ({
  selectedReason,
  onReasonChange,
  variant = 'list',
  className = '',
}) => {
  if (variant === 'select') {
    return (
      <IonSelect
        placeholder="Select wastage reason"
        value={selectedReason?.code}
        onSelectionChange={e => {
          const reason = WASTAGE_REASONS.find(r => r.code === e.detail.value);
          if (reason) onReasonChange(reason);
        }}
        className={className}
      >
        {WASTAGE_REASONS.map(reason => (
          <IonSelectOption key={reason.code} value={reason.code}>
            {reason.label}
          </IonSelectOption>
        ))}
      </IonSelect>
    );
  }

  if (variant === 'cards') {
    return (
      <div className={`grid grid-cols-1 md:grid-cols-2 gap-3 ${className}`}>
        {WASTAGE_REASONS.map(reason => (
          <IonCard
            key={reason.code}
            className={`cursor-pointer transition-all ${
              selectedReason?.code === reason.code
                ? 'ring-2 ring-primary border-primary'
                : 'hover:shadow-md'
            }`}
            onClick={() => onReasonChange(reason)}
          >
            <IonCardContent className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900 mb-1">
                    {reason.label}
                  </h3>
                  <div className="flex gap-1 flex-wrap">
                    {reason.requiresPhoto && (
                      <IonBadge color="warning" className="text-xs">
                        <IonIcon icon={camera} className="mr-1" size="small" />
                        Photo
                      </IonBadge>
                    )}
                    {reason.requiresNote && (
                      <IonBadge color="primary" className="text-xs">
                        <IonIcon icon={document} className="mr-1" size="small" />
                        Notes
                      </IonBadge>
                    )}
                  </div>
                </div>
                <IonRadio
                  value={reason.code}
                  checked={selectedReason?.code === reason.code}
                />
              </div>
            </IonCardContent>
          </IonCard>
        ))}
      </div>
    );
  }

  // Default list variant
  return (
    <IonRadioGroup
      value={selectedReason?.code}
      onIonChange={e => {
        const reason = WASTAGE_REASONS.find(r => r.code === e.detail.value);
        if (reason) onReasonChange(reason);
      }}
      className={className}
    >
      <IonList>
        {WASTAGE_REASONS.map(reason => (
          <IonItem key={reason.code}>
            <IonLabel>
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">{reason.label}</h3>
                  <div className="flex gap-2 mt-1">
                    {reason.requiresPhoto && (
                      <IonBadge color="warning" className="text-xs">
                        <IonIcon icon={camera} className="mr-1" size="small" />
                        Photo Required
                      </IonBadge>
                    )}
                    {reason.requiresNote && (
                      <IonBadge color="primary" className="text-xs">
                        <IonIcon icon={document} className="mr-1" size="small" />
                        Notes Required
                      </IonBadge>
                    )}
                  </div>
                </div>
              </div>
            </IonLabel>
            <IonRadio slot="end" value={reason.code} />
          </IonItem>
        ))}
      </IonList>
    </IonRadioGroup>
  );
};

export default WastageReasonSelector;