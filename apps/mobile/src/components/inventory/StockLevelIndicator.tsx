import React from 'react';
import { IonBadge, IonIcon, IonProgressBar } from '@ionic/react';
import { warning, checkmark, alert, close } from 'ionicons/icons';

interface StockLevelIndicatorProps {
  currentStock: number;
  minStock: number;
  maxStock: number;
  unit?: string;
  size?: 'small' | 'medium' | 'large';
  showProgress?: boolean;
  showText?: boolean;
  className?: string;
}

export type StockStatus = 'out' | 'low' | 'normal' | 'high';

export const getStockStatus = (current: number, min: number, max: number): StockStatus => {
  if (current <= 0) return 'out';
  if (current <= min) return 'low';
  if (current >= max * 0.8) return 'high';
  return 'normal';
};

export const getStockColor = (status: StockStatus): string => {
  switch (status) {
    case 'out': return 'danger';
    case 'low': return 'warning';
    case 'high': return 'success';
    case 'normal': return 'medium';
  }
};

export const getStockIcon = (status: StockStatus) => {
  switch (status) {
    case 'out': return close;
    case 'low': return warning;
    case 'high': return checkmark;
    case 'normal': return checkmark;
  }
};

export const getStockLabel = (status: StockStatus): string => {
  switch (status) {
    case 'out': return 'Out of Stock';
    case 'low': return 'Low Stock';
    case 'high': return 'Well Stocked';
    case 'normal': return 'Normal';
  }
};

const StockLevelIndicator: React.FC<StockLevelIndicatorProps> = ({
  currentStock,
  minStock,
  maxStock,
  unit = 'units',
  size = 'medium',
  showProgress = true,
  showText = true,
  className = '',
}) => {
  const status = getStockStatus(currentStock, minStock, maxStock);
  const color = getStockColor(status);
  const icon = getStockIcon(status);
  const label = getStockLabel(status);
  const percentage = maxStock > 0 ? Math.max((currentStock / maxStock) * 100, 2) : 0;

  const badgeSize = size === 'small' ? undefined : size === 'large' ? 'large' : undefined;
  
  return (
    <div className={`stock-level-indicator ${className}`}>
      <div className="flex items-center gap-2 mb-1">
        {showText && (
          <span className={`font-medium ${
            size === 'small' ? 'text-sm' : 
            size === 'large' ? 'text-lg' : 'text-base'
          }`}>
            {currentStock} {unit}
          </span>
        )}
        
        <IonBadge color={color} className={badgeSize ? `badge-${badgeSize}` : ''}>
          <IonIcon icon={icon} className="mr-1" />
          {size !== 'small' && label}
        </IonBadge>
      </div>

      {showProgress && (
        <div className="space-y-1">
          <div className="w-full bg-gray-200 rounded-full h-1.5">
            <div
              className={`h-1.5 rounded-full transition-all duration-300 ${
                status === 'out' ? 'bg-red-500' :
                status === 'low' ? 'bg-yellow-500' :
                status === 'high' ? 'bg-green-500' : 'bg-blue-500'
              }`}
              style={{ width: `${percentage}%` }}
            />
          </div>
          
          {size !== 'small' && (
            <div className="flex justify-between text-xs text-gray-500">
              <span>Min: {minStock}</span>
              <span>Max: {maxStock}</span>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default StockLevelIndicator;