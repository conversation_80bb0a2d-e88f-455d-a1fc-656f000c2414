import React, { useState } from 'react';
import {
  IonCard,
  IonCardContent,
  IonButton,
  IonIcon,
  IonChip,
  IonText,
  IonBadge,
  IonImg,
  IonSkeletonText,
} from '@ionic/react';
import {
  add,
  pricetag,
  warning,
  checkmarkCircle,
  eye,
} from 'ionicons/icons';
import { BranchInventoryItem } from '@/types/api';

interface ProductCardProps {
  item: BranchInventoryItem;
  onAddToCart: (item: BranchInventoryItem, quantity?: number) => Promise<boolean>;
  onViewDetails?: (item: BranchInventoryItem) => void;
  cartQuantity?: number;
  disabled?: boolean;
  compact?: boolean;
  showStock?: boolean;
  showCategory?: boolean;
}

export const ProductCard: React.FC<ProductCardProps> = ({
  item,
  onAddToCart,
  onViewDetails,
  cartQuantity = 0,
  disabled = false,
  compact = false,
  showStock = true,
  showCategory = true,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [imageError, setImageError] = useState(false);

  const isOutOfStock = item.stockLevel !== undefined && item.stockLevel === 0;
  const isLowStock = item.stockLevel !== undefined && item.stockLevel > 0 && item.stockLevel < 5;
  const isAvailable = item.isAvailable && !isOutOfStock;

  const handleAddToCart = async () => {
    if (disabled || !isAvailable) return;
    
    setIsLoading(true);
    try {
      await onAddToCart(item, 1);
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewDetails = () => {
    if (onViewDetails) {
      onViewDetails(item);
    }
  };

  if (compact) {
    return (
      <IonCard className={`product-card-compact ${!isAvailable ? 'opacity-60' : ''}`}>
        <IonCardContent className="p-3">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0 mr-3">
              <div className="flex items-start justify-between mb-1">
                <h3 className="font-medium text-sm truncate">{item.name}</h3>
                {cartQuantity > 0 && (
                  <IonBadge color="primary" size="small">
                    {cartQuantity}
                  </IonBadge>
                )}
              </div>
              
              <div className="flex items-center justify-between">
                <IonText color="primary">
                  <strong className="text-base">${item.price.toFixed(2)}</strong>
                </IonText>
                
                <div className="flex items-center space-x-1">
                  {showStock && item.stockLevel !== undefined && (
                    <span className="text-xs text-gray-500">
                      Stock: {item.stockLevel}
                    </span>
                  )}
                  
                  {(isOutOfStock || isLowStock) && (
                    <IonChip 
                      color={isOutOfStock ? 'danger' : 'warning'} 
                      size="small"
                    >
                      <IonIcon icon={warning} />
                      <span className="ml-1 text-xs">
                        {isOutOfStock ? 'Out' : 'Low'}
                      </span>
                    </IonChip>
                  )}
                </div>
              </div>
            </div>
            
            <IonButton
              size="small"
              onClick={handleAddToCart}
              disabled={disabled || !isAvailable || isLoading}
              fill={cartQuantity > 0 ? 'solid' : 'outline'}
            >
              <IonIcon icon={add} />
            </IonButton>
          </div>
        </IonCardContent>
      </IonCard>
    );
  }

  return (
    <IonCard className={`product-card ${!isAvailable ? 'opacity-60' : ''}`}>
      <div className="relative">
        {item.imageUrl && !imageError ? (
          <IonImg
            src={item.imageUrl}
            alt={item.name}
            className="h-32 w-full object-cover"
            onIonError={() => setImageError(true)}
          />
        ) : (
          <div className="h-32 w-full bg-gray-100 flex items-center justify-center">
            <IonIcon icon={pricetag} className="text-3xl text-gray-400" />
          </div>
        )}
        
        {/* Stock status badges */}
        <div className="absolute top-2 right-2 flex flex-col space-y-1">
          {cartQuantity > 0 && (
            <IonBadge color="primary">
              {cartQuantity} in cart
            </IonBadge>
          )}
          
          {isOutOfStock && (
            <IonChip color="danger" size="small">
              <IonIcon icon={warning} />
              <span className="ml-1">Out of Stock</span>
            </IonChip>
          )}
          
          {isLowStock && !isOutOfStock && (
            <IonChip color="warning" size="small">
              <IonIcon icon={warning} />
              <span className="ml-1">Low Stock</span>
            </IonChip>
          )}
          
          {isAvailable && !isLowStock && (
            <IonChip color="success" size="small">
              <IonIcon icon={checkmarkCircle} />
              <span className="ml-1">Available</span>
            </IonChip>
          )}
        </div>

        {/* Category badge */}
        {showCategory && (
          <div className="absolute top-2 left-2">
            <IonChip color="light" size="small">
              {item.category}
            </IonChip>
          </div>
        )}
      </div>

      <IonCardContent>
        <div className="flex items-start justify-between mb-2">
          <h3 className="font-semibold text-lg line-clamp-2">{item.name}</h3>
        </div>
        
        {item.description && (
          <p className="text-sm text-gray-600 mb-3 line-clamp-2">
            {item.description}
          </p>
        )}
        
        <div className="flex items-center justify-between mb-3">
          <IonText color="primary">
            <strong className="text-xl">${item.price.toFixed(2)}</strong>
          </IonText>
          
          {showStock && item.stockLevel !== undefined && (
            <div className="text-sm text-gray-500">
              Stock: {item.stockLevel}
            </div>
          )}
        </div>

        {/* Alternatives preview */}
        {item.alternatives && item.alternatives.length > 0 && (
          <div className="mb-3">
            <p className="text-xs text-gray-500 mb-1">
              +{item.alternatives.length} alternatives available
            </p>
          </div>
        )}

        {/* Action buttons */}
        <div className="flex space-x-2">
          <IonButton
            expand="block"
            onClick={handleAddToCart}
            disabled={disabled || !isAvailable || isLoading}
            className="flex-1"
          >
            <IonIcon icon={add} slot="start" />
            {isLoading ? 'Adding...' : 'Add to Cart'}
          </IonButton>
          
          {onViewDetails && (
            <IonButton
              fill="outline"
              onClick={handleViewDetails}
              size="default"
            >
              <IonIcon icon={eye} slot="icon-only" />
            </IonButton>
          )}
        </div>
      </IonCardContent>
    </IonCard>
  );
};