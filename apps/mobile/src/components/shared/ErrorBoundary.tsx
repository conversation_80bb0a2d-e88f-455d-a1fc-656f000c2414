import React, { Component, ErrorInfo, ReactNode } from 'react';
import { IonButton, IonContent, IonPage, IonIcon } from '@ionic/react';
import { warning, refresh } from 'ionicons/icons';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false
  };

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Uncaught error:', error, errorInfo);
    
    // Log to error reporting service if available
    if (typeof window !== 'undefined' && (window as any).Sentry) {
      (window as any).Sentry.captureException(error, {
        contexts: {
          react: {
            componentStack: errorInfo.componentStack
          }
        }
      });
    }
    
    this.setState({
      error,
      errorInfo
    });
  }

  private handleReload = () => {
    window.location.reload();
  };

  private handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  public render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <IonPage>
          <IonContent>
            <div className="flex min-h-screen flex-col items-center justify-center p-6 text-center">
              <div className="mb-6">
                <IonIcon 
                  icon={warning} 
                  className="text-6xl text-danger mb-4"
                />
              </div>
              
              <h1 className="mb-4 text-2xl font-bold text-gray-900">
                Something went wrong
              </h1>
              
              <p className="mb-2 text-gray-600 max-w-md">
                {this.state.error?.message || 'An unexpected error occurred while loading this page.'}
              </p>
              
              <p className="mb-6 text-sm text-gray-500 max-w-md">
                Please try refreshing the page or contact support if the problem persists.
              </p>

              <div className="flex flex-col sm:flex-row gap-3">
                <IonButton 
                  onClick={this.handleRetry}
                  fill="outline"
                  color="primary"
                >
                  Try Again
                </IonButton>
                
                <IonButton 
                  onClick={this.handleReload}
                  color="primary"
                >
                  <IonIcon icon={refresh} slot="start" />
                  Reload Page
                </IonButton>
              </div>

              {/* Show error details in development */}
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <details className="mt-8 max-w-2xl">
                  <summary className="cursor-pointer text-sm text-gray-500 mb-2">
                    Show error details (development only)
                  </summary>
                  <div className="text-left bg-gray-100 p-4 rounded-lg text-xs overflow-auto">
                    <div className="mb-2">
                      <strong>Error:</strong> {this.state.error.message}
                    </div>
                    <div className="mb-2">
                      <strong>Stack:</strong>
                      <pre className="whitespace-pre-wrap mt-1">{this.state.error.stack}</pre>
                    </div>
                    {this.state.errorInfo && (
                      <div>
                        <strong>Component Stack:</strong>
                        <pre className="whitespace-pre-wrap mt-1">{this.state.errorInfo.componentStack}</pre>
                      </div>
                    )}
                  </div>
                </details>
              )}
            </div>
          </IonContent>
        </IonPage>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;