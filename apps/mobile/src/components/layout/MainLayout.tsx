import React from 'react';
import { IonContent, IonPage, IonSplitPane } from '@ionic/react';
import SideMenu from '../navigation/SideMenu';
import BottomNav from '../navigation/BottomNav';
import { ErrorBoundary } from '../shared';

interface MainLayoutProps {
  children: React.ReactNode;
  title?: string;
  showBottomNav?: boolean;
  showSideMenu?: boolean;
}

const MainLayout: React.FC<MainLayoutProps> = ({ 
  children, 
  title,
  showBottomNav = true,
  showSideMenu = true 
}) => {
  return (
    <ErrorBoundary>
      <IonPage>
        <IonSplitPane contentId="main-content" when="md">
          {showSideMenu && <SideMenu />}
          <IonContent id="main-content" className="ion-padding">
            {children}
          </IonContent>
        </IonSplitPane>
        {showBottomNav && <BottomNav />}
      </IonPage>
    </ErrorBoundary>
  );
};

export default MainLayout;