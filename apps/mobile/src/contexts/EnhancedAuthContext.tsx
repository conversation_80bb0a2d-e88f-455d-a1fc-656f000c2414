import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { db } from '../services/db';
import { syncService } from '../services/syncService';
import { authService } from '../services/authService';
import { Company, Location, POSUser } from '../types/api';

interface AuthContextType {
  // Authentication state
  isAuthenticated: boolean;
  currentUser: POSUser | null;
  
  // Company and location state
  company: Company | null;
  location: Location | null;
  companies: Company[];
  locations: Location[];
  
  // Connection state
  isOnline: boolean;
  isSyncing: boolean;
  syncStatus: {
    pendingCount: number;
    lastSync?: Date;
  };
  
  // Authentication methods
  setCompany: (company: Company) => Promise<void>;
  setLocation: (location: Location) => Promise<void>;
  loginWithPin: (pin: string) => Promise<void>;
  logout: () => Promise<void>;
  
  // Data management
  refreshCompanies: () => Promise<void>;
  refreshLocations: (companyId: string) => Promise<void>;
  forceSync: () => Promise<void>;
  
  // Validation
  validateOfflinePin: (pin: string) => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const EnhancedAuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  // Authentication state
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [currentUser, setCurrentUser] = useState<POSUser | null>(null);
  
  // Company and location state
  const [company, setCompanyState] = useState<Company | null>(null);
  const [location, setLocationState] = useState<Location | null>(null);
  const [companies, setCompanies] = useState<Company[]>([]);
  const [locations, setLocations] = useState<Location[]>([]);
  
  // Connection state
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [isSyncing, setIsSyncing] = useState(false);
  const [syncStatus, setSyncStatus] = useState({
    pendingCount: 0,
    lastSync: undefined as Date | undefined
  });

  // Initialize context on mount
  useEffect(() => {
    initializeAuth();
    setupNetworkListeners();
    setupSyncStatusUpdates();
    
    return () => {
      cleanupNetworkListeners();
      syncService.stopSync();
    };
  }, []);

  // Monitor sync status when authenticated
  useEffect(() => {
    if (isAuthenticated && company && location) {
      updateSyncStatus();
      const interval = setInterval(updateSyncStatus, 5000); // Update every 5 seconds
      return () => clearInterval(interval);
    }
  }, [isAuthenticated, company, location]);

  /**
   * Initialize authentication state from storage
   */
  const initializeAuth = async () => {
    try {
      // Load saved authentication state
      const savedAuth = await db.getAppState('isAuthenticated');
      const savedUser = await db.getAppState('currentUser');
      const savedCompany = await db.getAppState('currentCompany');
      const savedLocation = await db.getAppState('currentLocation');

      if (savedAuth && savedUser && savedCompany && savedLocation) {
        setIsAuthenticated(true);
        setCurrentUser(savedUser);
        setCompanyState(savedCompany);
        setLocationState(savedLocation);

        // Start sync if online
        if (navigator.onLine) {
          syncService.startSync(savedCompany.id, savedLocation.id);
        }
      }

      // Load cached companies and locations
      const cachedCompanies = await db.getAppState('cachedCompanies');
      const cachedLocations = await db.getAppState('cachedLocations');
      
      if (cachedCompanies) setCompanies(cachedCompanies);
      if (cachedLocations) setLocations(cachedLocations);

    } catch (error) {
      console.error('Failed to initialize auth:', error);
    }
  };

  /**
   * Setup network status listeners
   */
  const setupNetworkListeners = () => {
    const handleOnline = () => {
      setIsOnline(true);
      if (isAuthenticated && company && location) {
        syncService.startSync(company.id, location.id);
      }
    };

    const handleOffline = () => {
      setIsOnline(false);
      syncService.stopSync();
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return { handleOnline, handleOffline };
  };

  /**
   * Cleanup network listeners
   */
  const cleanupNetworkListeners = () => {
    window.removeEventListener('online', () => {});
    window.removeEventListener('offline', () => {});
  };

  /**
   * Setup sync status updates
   */
  const setupSyncStatusUpdates = () => {
    // Listen for sync events if needed
    // This could be enhanced with custom events from syncService
  };

  /**
   * Update sync status information
   */
  const updateSyncStatus = async () => {
    try {
      const status = await syncService.getSyncStatus();
      setIsSyncing(status.isSyncing);
      setSyncStatus({
        pendingCount: status.pendingCount,
        lastSync: status.lastSync
      });
    } catch (error) {
      console.error('Failed to update sync status:', error);
    }
  };

  /**
   * Set current company
   */
  const setCompany = async (selectedCompany: Company) => {
    try {
      setCompanyState(selectedCompany);
      await db.setAppState('currentCompany', selectedCompany);

      // Clear location when company changes
      setLocationState(null);
      await db.setAppState('currentLocation', null);

      // Refresh locations for the new company
      if (isOnline) {
        await refreshLocations(selectedCompany.id);
      } else {
        // Load cached locations for this company
        const cachedLocations = await db.getAppState(`locations_${selectedCompany.id}`);
        if (cachedLocations) {
          setLocations(cachedLocations);
        }
      }
    } catch (error) {
      console.error('Failed to set company:', error);
      throw error;
    }
  };

  /**
   * Set current location
   */
  const setLocation = async (selectedLocation: Location) => {
    try {
      setLocationState(selectedLocation);
      await db.setAppState('currentLocation', selectedLocation);
    } catch (error) {
      console.error('Failed to set location:', error);
      throw error;
    }
  };

  /**
   * Login with PIN
   */
  const loginWithPin = async (pin: string) => {
    if (!company || !location) {
      throw new Error('Company and location must be selected first');
    }

    try {
      if (isOnline) {
        // Online authentication
        const user = await authService.loginWithPin(pin, company.id, location.id);
        
        setCurrentUser(user);
        setIsAuthenticated(true);
        
        // Save authentication state
        await db.setAppState('currentUser', user);
        await db.setAppState('isAuthenticated', true);
        
        // Store PIN hash for offline use (in production, use proper hashing)
        await db.setAppState(`pin_hash_${company.id}_${location.id}`, pin);
        
        // Start initial sync
        setIsSyncing(true);
        try {
          await syncService.initialSync(company.id, location.id);
          syncService.startSync(company.id, location.id);
        } catch (syncError) {
          console.error('Initial sync failed:', syncError);
          // Don't fail authentication if sync fails
        } finally {
          setIsSyncing(false);
        }
        
      } else {
        // Offline authentication
        const isValidPin = await validateOfflinePin(pin);
        if (!isValidPin) {
          throw new Error('Invalid PIN');
        }

        // Load offline user data
        const offlineUser = await db.getAppState(`offline_user_${company.id}_${location.id}`);
        if (!offlineUser) {
          throw new Error('No offline user data available');
        }

        setCurrentUser(offlineUser);
        setIsAuthenticated(true);
        
        await db.setAppState('currentUser', offlineUser);
        await db.setAppState('isAuthenticated', true);
      }
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  };

  /**
   * Validate PIN for offline use
   */
  const validateOfflinePin = async (pin: string): Promise<boolean> => {
    if (!company || !location) {
      return false;
    }

    try {
      const savedPinHash = await db.getAppState(`pin_hash_${company.id}_${location.id}`);
      
      // In production, use proper hash comparison
      return savedPinHash === pin;
    } catch (error) {
      console.error('Offline PIN validation failed:', error);
      return false;
    }
  };

  /**
   * Logout user
   */
  const logout = async () => {
    try {
      // Stop sync
      syncService.stopSync();
      
      // Clear authentication state
      setIsAuthenticated(false);
      setCurrentUser(null);
      
      // Clear stored authentication data
      await db.setAppState('isAuthenticated', false);
      await db.setAppState('currentUser', null);
      
      // Optionally clear company/location
      // setCompanyState(null);
      // setLocationState(null);
      // await db.setAppState('currentCompany', null);
      // await db.setAppState('currentLocation', null);
      
      // Clear API service credentials
      authService.logout();
      
    } catch (error) {
      console.error('Logout failed:', error);
      throw error;
    }
  };

  /**
   * Refresh companies list
   */
  const refreshCompanies = async () => {
    if (!isOnline) {
      return;
    }

    try {
      const fetchedCompanies = await authService.getCompanies();
      setCompanies(fetchedCompanies);
      
      // Cache companies
      await db.setAppState('cachedCompanies', fetchedCompanies);
    } catch (error) {
      console.error('Failed to refresh companies:', error);
      throw error;
    }
  };

  /**
   * Refresh locations for a company
   */
  const refreshLocations = async (companyId: string) => {
    if (!isOnline) {
      return;
    }

    try {
      const fetchedLocations = await authService.getLocations(companyId);
      setLocations(fetchedLocations);
      
      // Cache locations for this company
      await db.setAppState(`locations_${companyId}`, fetchedLocations);
      await db.setAppState('cachedLocations', fetchedLocations);
    } catch (error) {
      console.error('Failed to refresh locations:', error);
      throw error;
    }
  };

  /**
   * Force sync now
   */
  const forceSync = async () => {
    if (!isOnline || !company || !location) {
      return;
    }

    try {
      await syncService.forcSync();
      await updateSyncStatus();
    } catch (error) {
      console.error('Force sync failed:', error);
      throw error;
    }
  };

  const contextValue: AuthContextType = {
    // Authentication state
    isAuthenticated,
    currentUser,
    
    // Company and location state
    company,
    location,
    companies,
    locations,
    
    // Connection state
    isOnline,
    isSyncing,
    syncStatus,
    
    // Authentication methods
    setCompany,
    setLocation,
    loginWithPin,
    logout,
    
    // Data management
    refreshCompanies,
    refreshLocations,
    forceSync,
    
    // Validation
    validateOfflinePin
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export const useEnhancedAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useEnhancedAuth must be used within an EnhancedAuthProvider');
  }
  return context;
};

// Backward compatibility - export as useAuth as well
export const useAuth = useEnhancedAuth;