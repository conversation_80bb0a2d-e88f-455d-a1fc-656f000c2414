import { useEffect, useState } from 'react';
import { Capacitor } from '@capacitor/core';
import { App, AppState } from '@capacitor/app';
import { StatusBar, Style } from '@capacitor/status-bar';
import { Keyboard, KeyboardInfo } from '@capacitor/keyboard';
import { Haptics, ImpactStyle } from '@capacitor/haptics';

interface UseCapacitorReturn {
  isNative: boolean;
  platform: string;
  appState: AppState | null;
  keyboardVisible: boolean;
  statusBarHeight: number;
  vibrate: (style?: ImpactStyle) => Promise<void>;
  setStatusBarStyle: (style: Style) => Promise<void>;
}

export const useCapacitor = (): UseCapacitorReturn => {
  const [appState, setAppState] = useState<AppState | null>(null);
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const [statusBarHeight, setStatusBarHeight] = useState(0);

  const isNative = Capacitor.isNativePlatform();
  const platform = Capacitor.getPlatform();

  // Vibration function
  const vibrate = async (style: ImpactStyle = ImpactStyle.Medium) => {
    if (isNative) {
      try {
        await Haptics.impact({ style });
      } catch (error) {
        console.warn('Haptics not available:', error);
      }
    }
  };

  // Status bar style function
  const setStatusBarStyle = async (style: Style) => {
    if (isNative) {
      try {
        await StatusBar.setStyle({ style });
      } catch (error) {
        console.warn('StatusBar not available:', error);
      }
    }
  };

  useEffect(() => {
    // App state listeners
    const setupAppListeners = async () => {
      if (isNative) {
        try {
          // Get initial app state
          const state = await App.getState();
          setAppState(state);

          // Listen for app state changes
          App.addListener('appStateChange', setAppState);

          // Listen for back button (Android)
          App.addListener('backButton', (event) => {
            // Handle back button press
            console.log('Back button pressed:', event);
          });
        } catch (error) {
          console.warn('App listeners setup failed:', error);
        }
      }
    };

    setupAppListeners();

    return () => {
      App.removeAllListeners();
    };
  }, [isNative]);

  useEffect(() => {
    // Keyboard listeners
    const setupKeyboardListeners = async () => {
      if (isNative) {
        try {
          Keyboard.addListener('keyboardWillShow', (info: KeyboardInfo) => {
            setKeyboardVisible(true);
          });

          Keyboard.addListener('keyboardWillHide', () => {
            setKeyboardVisible(false);
          });
        } catch (error) {
          console.warn('Keyboard listeners setup failed:', error);
        }
      }
    };

    setupKeyboardListeners();

    return () => {
      Keyboard.removeAllListeners();
    };
  }, [isNative]);

  useEffect(() => {
    // Status bar setup
    const setupStatusBar = async () => {
      if (isNative) {
        try {
          const info = await StatusBar.getInfo();
          setStatusBarHeight(info.height);
          
          // Set default status bar style
          await StatusBar.setStyle({ style: Style.Default });
        } catch (error) {
          console.warn('StatusBar setup failed:', error);
        }
      }
    };

    setupStatusBar();
  }, [isNative]);

  return {
    isNative,
    platform,
    appState,
    keyboardVisible,
    statusBarHeight,
    vibrate,
    setStatusBarStyle,
  };
};