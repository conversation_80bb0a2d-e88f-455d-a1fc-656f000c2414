// src/middleware/cors-middleware.ts
import { NextRequest, NextResponse } from 'next/server';

// Check if we're in development mode
const isDevelopment = process.env.NODE_ENV === 'development';

// Define allowed origins
const allowedOrigins = [
  // Ionic app development origins
  'http://localhost:8100',  // Primary local development
  'http://localhost:8101',  // Alternative Ionic dev port
  'http://localhost',       // General local development
  'capacitor://localhost',  // Capacitor local development
  'ionic://localhost',      // Ionic framework local development
  
  // Expanded local network and loopback variations
  'http://127.0.0.1:8100',  // Localhost loopback
  'http://127.0.0.1:8101',  // Alternative Ionic dev port loopback
  'http://*************:8100',  // Common local network IP
  'http://*************:8100',  // Alternative local network IP
  
  // Next.js development server
  'http://localhost:3000',
  'http://127.0.0.1:3000',
  'null', // For some localhost requests
  
  // Potential production or staging origins (if applicable)
  'https://your-ionic-app-domain.com',  // Replace with actual domain if needed
  'https://staging.your-ionic-app-domain.com'  // Staging environment
];

/**
 * CORS middleware for Next.js API routes
 * This middleware applies CORS headers to all API responses
 * to allow communication with the Ionic POS app
 */
export function corsMiddleware(req: NextRequest, res: NextResponse) {
  // Get the origin from the request
  const origin = req.headers.get('origin') || '';
  
  // Check if the origin is allowed
  const isAllowedOrigin = allowedOrigins.includes(origin);
  
  // For preflight requests (OPTIONS)
  if (req.method === 'OPTIONS') {
    const headers = new Headers();
    
    // In development or for allowed origins, set Access-Control-Allow-Origin
    if (isAllowedOrigin || isDevelopment) {
      headers.set('Access-Control-Allow-Origin', origin || '*');
    }
    
    headers.set('Access-Control-Allow-Credentials', 'true');
    headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    headers.set('Access-Control-Allow-Headers', 'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization, company-id, X-API-Key, X-Company-Id, X-Location-Id, Cache-Control');
    headers.set('Access-Control-Max-Age', '86400'); // 24 hours
    
    // Return 204 No Content for preflight requests
    return new NextResponse(null, {
      status: 204,
      headers,
    });
  }
  
  // For regular requests
  const headers = new Headers(res.headers);
  
  // In development or for allowed origins, set Access-Control-Allow-Origin
  if (isAllowedOrigin || isDevelopment) {
    headers.set('Access-Control-Allow-Origin', origin || '*');
  } else {
    // Log non-allowed origins for debugging
    console.warn(`Request from non-allowed origin: ${origin}`);
  }
  
  headers.set('Access-Control-Allow-Credentials', 'true');
  headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  headers.set('Access-Control-Allow-Headers', 'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization, company-id, X-API-Key, X-Company-Id, X-Location-Id, Cache-Control');
  
  // Create a new response with the updated headers
  const response = NextResponse.next({
    request: {
      headers: req.headers,
    },
  });
  
  // Copy all headers to the new response
  headers.forEach((value, key) => {
    response.headers.set(key, value);
  });
  
  return response;
}

/**
 * Apply CORS headers to a NextResponse
 * @param response The NextResponse to apply headers to
 * @param req The original NextRequest
 * @returns The response with CORS headers
 */
export function applyCorsHeaders(response: NextResponse, req: NextRequest): NextResponse {
  // Get the origin from the request
  const origin = req.headers.get('origin') || '';
  
  // Check if the origin is allowed
  const isAllowedOrigin = allowedOrigins.includes(origin);
  
  // In development or for allowed origins, set Access-Control-Allow-Origin
  if (isAllowedOrigin || isDevelopment) {
    response.headers.set('Access-Control-Allow-Origin', origin || '*');
  }
  
  response.headers.set('Access-Control-Allow-Credentials', 'true');
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  response.headers.set('Access-Control-Allow-Headers', 'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization, company-id, X-API-Key, X-Company-Id, X-Location-Id');
  
  return response;
}

/**
 * Handle CORS preflight requests (OPTIONS)
 * @param req The NextRequest object
 * @returns A NextResponse with appropriate CORS headers
 */
export function handleCorsOptions(req: NextRequest): NextResponse {
  // Get the origin from the request
  const origin = req.headers.get('origin') || '';
  
  // Check if the origin is allowed
  const isAllowedOrigin = allowedOrigins.includes(origin);
  
  // Prepare headers for preflight response
  const headers = new Headers();
  
  // In development or for allowed origins, set Access-Control-Allow-Origin
  if (isAllowedOrigin || isDevelopment) {
    headers.set('Access-Control-Allow-Origin', origin || '*');
  }
  
  headers.set('Access-Control-Allow-Credentials', 'true');
  headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  headers.set('Access-Control-Allow-Headers', 'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization, company-id, X-API-Key, X-Company-Id, X-Location-Id, Cache-Control');
  headers.set('Access-Control-Max-Age', '86400'); // 24 hours
  
  // Return 204 No Content for preflight requests
  return new NextResponse(null, {
    status: 204,
    headers,
  });
}
