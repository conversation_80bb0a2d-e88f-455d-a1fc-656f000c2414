// Order types
export interface Order {
  id: string;
  orderNumber: string;
  customerId?: string;
  items: OrderItem[];
  status: OrderStatus;
  total: number;
  tax: number;
  subtotal: number;
  companyId: string;
  locationId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface OrderItem {
  id: string;
  productId: string;
  name: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

export enum OrderStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  PREPARING = 'preparing',
  READY = 'ready',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}