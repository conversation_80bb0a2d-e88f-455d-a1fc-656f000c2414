/**
 * API-related type definitions for FoodPrepAI ecosystem
 * Includes request/response types, error handling, and API client interfaces
 */

import { z } from 'zod';
import { 
  ApiResponse, 
  ApiError, 
  PaginationParams, 
  PaginatedResponse,
  ObjectId,
  CompanyId,
  LocationId,
  UserId
} from '../common';
import { User, CreateUserInput, UpdateUserInput, UserProfile, UserSyncData } from '../user';
import { Company, Location, CreateCompanyInput, UpdateCompanyInput } from '../company';
import { Ingredient, CreateIngredientInput, UpdateIngredientInput } from '../inventory';
import { Order, CreateOrderInput, UpdateOrderInput, OrderSummary } from '../orders';

// Generic API request wrapper
export interface ApiRequest<T = any> {
  data?: T;
  params?: Record<string, string>;
  query?: Record<string, any>;
  headers?: Record<string, string>;
  meta?: {
    timestamp: string;
    requestId: string;
    source: string;
  };
}

// Authentication-related API types
export namespace AuthAPI {
  export interface LoginRequest {
    email: string;
    password: string;
    companyCode?: string;
  }

  export interface LoginResponse {
    user: UserProfile;
    token: string;
    refreshToken: string;
    expiresIn: number;
    company?: {
      _id: CompanyId;
      name: string;
      subdomain: string;
    };
  }

  export interface SignupRequest {
    email: string;
    password: string;
    name?: string;
    companyName: string;
    subdomain: string;
  }

  export interface RefreshTokenRequest {
    refreshToken: string;
  }

  export interface ChangePasswordRequest {
    currentPassword: string;
    newPassword: string;
  }

  export interface ResetPasswordRequest {
    email: string;
    companyCode?: string;
  }

  export interface ResetPasswordConfirmRequest {
    token: string;
    newPassword: string;
  }

  export interface VerifyTokenResponse {
    valid: boolean;
    user?: UserProfile;
    company?: {
      _id: CompanyId;
      name: string;
    };
  }
}

// User management API types
export namespace UserAPI {
  export interface ListUsersQuery extends PaginationParams {
    search?: string;
    role?: string;
    userType?: string;
    locationId?: LocationId;
    isActive?: boolean;
  }

  export interface GetUserResponse extends ApiResponse<UserProfile> {}
  export interface ListUsersResponse extends ApiResponse<PaginatedResponse<UserProfile>> {}
  export interface CreateUserResponse extends ApiResponse<UserProfile> {}
  export interface UpdateUserResponse extends ApiResponse<UserProfile> {}
  export interface DeleteUserResponse extends ApiResponse<{ deleted: boolean }> {}
  
  export interface SyncUsersQuery {
    lastSyncTimestamp?: string;
    locationId?: LocationId;
    limit?: number;
  }
  
  export interface SyncUsersResponse extends ApiResponse<UserSyncData> {}
}

// Company management API types
export namespace CompanyAPI {
  export interface ListCompaniesQuery extends PaginationParams {
    search?: string;
    status?: string;
    ownerId?: UserId;
  }

  export interface GetCompanyResponse extends ApiResponse<Company> {}
  export interface ListCompaniesResponse extends ApiResponse<PaginatedResponse<Company>> {}
  export interface CreateCompanyResponse extends ApiResponse<Company> {}
  export interface UpdateCompanyResponse extends ApiResponse<Company> {}
  export interface DeleteCompanyResponse extends ApiResponse<{ deleted: boolean }> {}
}

// Location management API types
export namespace LocationAPI {
  export interface ListLocationsQuery extends PaginationParams {
    search?: string;
    locationType?: string;
    canSellToExternal?: boolean;
    canDoTransfers?: boolean;
    isActive?: boolean;
  }

  export interface GetLocationResponse extends ApiResponse<Location> {}
  export interface ListLocationsResponse extends ApiResponse<PaginatedResponse<Location>> {}
  export interface CreateLocationResponse extends ApiResponse<Location> {}
  export interface UpdateLocationResponse extends ApiResponse<Location> {}
  export interface DeleteLocationResponse extends ApiResponse<{ deleted: boolean }> {}
}

// Inventory management API types
export namespace InventoryAPI {
  export interface ListIngredientsQuery extends PaginationParams {
    search?: string;
    category?: string;
    supplierId?: string;
    canBeSold?: boolean;
    isActive?: boolean;
    lowStock?: boolean;
  }

  export interface GetIngredientResponse extends ApiResponse<Ingredient> {}
  export interface ListIngredientsResponse extends ApiResponse<PaginatedResponse<Ingredient>> {}
  export interface CreateIngredientResponse extends ApiResponse<Ingredient> {}
  export interface UpdateIngredientResponse extends ApiResponse<Ingredient> {}
  export interface DeleteIngredientResponse extends ApiResponse<{ deleted: boolean }> {}

  export interface InventoryMovementRequest {
    ingredientId: string;
    locationId: LocationId;
    movementType: string;
    quantity: number;
    unitPrice?: number;
    notes?: string;
    referenceType?: string;
    referenceId?: string;
  }

  export interface InventoryMovementResponse extends ApiResponse<{
    transactionId: string;
    newStock: number;
  }> {}

  export interface StockLevelsQuery {
    locationId?: LocationId;
    ingredientIds?: string[];
    lowStockOnly?: boolean;
  }

  export interface StockLevel {
    ingredientId: string;
    ingredientName: string;
    currentStock: number;
    reorderPoint?: number;
    isLowStock: boolean;
  }

  export interface StockLevelsResponse extends ApiResponse<StockLevel[]> {}
}

// Order management API types
export namespace OrderAPI {
  export interface ListOrdersQuery extends PaginationParams {
    search?: string;
    status?: string;
    buyerLocationId?: LocationId;
    sellerLocationId?: LocationId;
    dateFrom?: string;
    dateTo?: string;
    priority?: string;
    orderSource?: string;
  }

  export interface GetOrderResponse extends ApiResponse<Order> {}
  export interface ListOrdersResponse extends ApiResponse<PaginatedResponse<OrderSummary>> {}
  export interface CreateOrderResponse extends ApiResponse<Order> {}
  export interface UpdateOrderResponse extends ApiResponse<Order> {}
  export interface DeleteOrderResponse extends ApiResponse<{ deleted: boolean }> {}

  export interface OrderSyncRequest {
    operation: 'CREATE' | 'UPDATE' | 'DELETE';
    order: Order | Partial<Order>;
    source?: string;
  }

  export interface OrderSyncResponse extends ApiResponse<{
    orderId: string;
    syncStatus: string;
    message: string;
  }> {}
}

// Reports and analytics API types
export namespace ReportsAPI {
  export interface ReportQuery {
    startDate: string;
    endDate: string;
    locationIds?: LocationId[];
    groupBy?: 'day' | 'week' | 'month';
  }

  export interface SalesReportResponse extends ApiResponse<{
    totalSales: number;
    totalOrders: number;
    averageOrderValue: number;
    topItems: Array<{
      itemId: string;
      itemName: string;
      quantity: number;
      revenue: number;
    }>;
    dailyBreakdown: Array<{
      date: string;
      sales: number;
      orders: number;
    }>;
  }> {}

  export interface InventoryReportResponse extends ApiResponse<{
    totalValue: number;
    totalItems: number;
    lowStockItems: number;
    topValueItems: Array<{
      ingredientId: string;
      ingredientName: string;
      currentStock: number;
      value: number;
    }>;
  }> {}
}

// POS-specific API types
export namespace POSAPI {
  export interface POSAuthRequest {
    pin: string;
    locationId: LocationId;
  }

  export interface POSAuthResponse extends ApiResponse<{
    user: UserProfile;
    token: string;
    location: Location;
    permissions: string[];
  }> {}

  export interface POSSaleRequest {
    locationId: LocationId;
    items: Array<{
      itemId: string;
      itemType: 'INGREDIENT' | 'RECIPE';
      quantity: number;
      unitPrice: number;
    }>;
    paymentMethod: string;
    customerInfo?: {
      name?: string;
      phone?: string;
      email?: string;
    };
    discountPercent?: number;
    notes?: string;
  }

  export interface POSSaleResponse extends ApiResponse<{
    saleId: string;
    receiptNumber: string;
    totalAmount: number;
    paymentStatus: string;
  }> {}
}

// Error handling types
export interface ValidationError extends ApiError {
  field: string;
  value: any;
  constraint: string;
}

export interface APIErrorResponse {
  success: false;
  error: string;
  message?: string;
  details?: ApiError[];
  timestamp: string;
  path?: string;
  method?: string;
  statusCode: number;
}

// API client configuration
export interface APIClientConfig {
  baseURL: string;
  timeout: number;
  headers: Record<string, string>;
  retryAttempts: number;
  retryDelay: number;
}

// API client interface
export interface APIClient {
  // Authentication
  login(data: AuthAPI.LoginRequest): Promise<AuthAPI.LoginResponse>;
  signup(data: AuthAPI.SignupRequest): Promise<ApiResponse>;
  refreshToken(data: AuthAPI.RefreshTokenRequest): Promise<AuthAPI.LoginResponse>;
  logout(): Promise<ApiResponse>;
  
  // Users
  getUser(id: UserId): Promise<UserAPI.GetUserResponse>;
  listUsers(query?: UserAPI.ListUsersQuery): Promise<UserAPI.ListUsersResponse>;
  createUser(data: CreateUserInput): Promise<UserAPI.CreateUserResponse>;
  updateUser(id: UserId, data: UpdateUserInput): Promise<UserAPI.UpdateUserResponse>;
  deleteUser(id: UserId): Promise<UserAPI.DeleteUserResponse>;
  
  // Companies
  getCompany(id: CompanyId): Promise<CompanyAPI.GetCompanyResponse>;
  listCompanies(query?: CompanyAPI.ListCompaniesQuery): Promise<CompanyAPI.ListCompaniesResponse>;
  createCompany(data: CreateCompanyInput): Promise<CompanyAPI.CreateCompanyResponse>;
  updateCompany(id: CompanyId, data: UpdateCompanyInput): Promise<CompanyAPI.UpdateCompanyResponse>;
  
  // Locations
  getLocation(id: LocationId): Promise<LocationAPI.GetLocationResponse>;
  listLocations(query?: LocationAPI.ListLocationsQuery): Promise<LocationAPI.ListLocationsResponse>;
  
  // Inventory
  getIngredient(id: string): Promise<InventoryAPI.GetIngredientResponse>;
  listIngredients(query?: InventoryAPI.ListIngredientsQuery): Promise<InventoryAPI.ListIngredientsResponse>;
  createIngredient(data: CreateIngredientInput): Promise<InventoryAPI.CreateIngredientResponse>;
  updateIngredient(id: string, data: UpdateIngredientInput): Promise<InventoryAPI.UpdateIngredientResponse>;
  recordInventoryMovement(data: InventoryAPI.InventoryMovementRequest): Promise<InventoryAPI.InventoryMovementResponse>;
  getStockLevels(query?: InventoryAPI.StockLevelsQuery): Promise<InventoryAPI.StockLevelsResponse>;
  
  // Orders
  getOrder(id: ObjectId): Promise<OrderAPI.GetOrderResponse>;
  listOrders(query?: OrderAPI.ListOrdersQuery): Promise<OrderAPI.ListOrdersResponse>;
  createOrder(data: CreateOrderInput): Promise<OrderAPI.CreateOrderResponse>;
  updateOrder(id: ObjectId, data: UpdateOrderInput): Promise<OrderAPI.UpdateOrderResponse>;
  syncOrder(data: OrderAPI.OrderSyncRequest): Promise<OrderAPI.OrderSyncResponse>;
  
  // Reports
  getSalesReport(query: ReportsAPI.ReportQuery): Promise<ReportsAPI.SalesReportResponse>;
  getInventoryReport(query: ReportsAPI.ReportQuery): Promise<ReportsAPI.InventoryReportResponse>;
  
  // POS
  posAuth(data: POSAPI.POSAuthRequest): Promise<POSAPI.POSAuthResponse>;
  posSale(data: POSAPI.POSSaleRequest): Promise<POSAPI.POSSaleResponse>;
}

// HTTP method types
export type HTTPMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

// Request configuration
export interface RequestConfig {
  method: HTTPMethod;
  url: string;
  data?: any;
  params?: Record<string, any>;
  headers?: Record<string, string>;
  timeout?: number;
  retries?: number;
}

// Response configuration
export interface ResponseConfig<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
  config: RequestConfig;
}

// Zod schemas for API validation
export const PaginationParamsSchema = z.object({
  page: z.number().min(1).optional().default(1),
  limit: z.number().min(1).max(100).optional().default(20),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional().default('asc')
});

export const ApiResponseSchema = <T extends z.ZodType>(dataSchema: T) => z.object({
  success: z.boolean(),
  data: dataSchema.optional(),
  message: z.string().optional(),
  error: z.string().optional(),
  timestamp: z.string()
});

// Helper functions for API responses
export const createSuccessResponse = <T>(data?: T, message?: string): ApiResponse<T> => ({
  success: true,
  ...(data !== undefined && { data }),
  ...(message !== undefined && { message }),
  timestamp: new Date().toISOString()
});

export const createErrorResponse = (error: string, message?: string): APIErrorResponse => ({
  success: false,
  error,
  ...(message !== undefined && { message }),
  timestamp: new Date().toISOString(),
  statusCode: 400
});

export const createValidationErrorResponse = (errors: ValidationError[]): APIErrorResponse => ({
  success: false,
  error: 'VALIDATION_ERROR',
  message: 'Request validation failed',
  details: errors,
  timestamp: new Date().toISOString(),
  statusCode: 422
});