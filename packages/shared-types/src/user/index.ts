/**
 * User-related type definitions for FoodPrepAI ecosystem
 */

import { z } from 'zod';
import { 
  BaseDocument, 
  ObjectId, 
  UserType, 
  SystemRole, 
  SyncStatus,
  CompanyId,
  LocationId,
  RoleId,
  UserId
} from '../common';

// Core User interface
export interface User extends BaseDocument {
  _id: UserId;
  email: string;
  passwordHash: string;
  name?: string;
  displayName?: string;
  userType: UserType;
  companyId?: CompanyId;
  role?: SystemRole | ObjectId; // Can be system role or custom role ID
  permissions?: string[];
  customRoleId?: RoleId;
  lastModified?: Date;
  syncStatus?: SyncStatus;
  modifiedFields?: string[];
  pin?: string;
  isDeleted?: boolean;
  locationIds: LocationId[]; // Array of location IDs
  locationId?: LocationId; // Primary location assignment
  canUseIonicApp?: boolean;
  posAccess?: boolean;
  posSettings?: POSSettings;
  locationsAccess?: LocationId[];
}

// POS-specific settings for users
export interface POSSettings {
  defaultRegister?: string;
  canOpenDrawer?: boolean;
  canGiveDiscount?: boolean;
  canVoidSales?: boolean;
  [key: string]: any;
}

// User creation input (for registration/admin creation)
export interface CreateUserInput {
  email: string;
  password: string;
  name?: string;
  displayName?: string;
  userType: UserType;
  companyId?: CompanyId;
  role?: SystemRole | ObjectId;
  permissions?: string[];
  locationIds?: LocationId[];
  locationId?: LocationId;
  canUseIonicApp?: boolean;
  posAccess?: boolean;
  posSettings?: POSSettings;
}

// User update input
export interface UpdateUserInput {
  name?: string;
  displayName?: string;
  role?: SystemRole | ObjectId;
  permissions?: string[];
  locationIds?: LocationId[];
  locationId?: LocationId;
  canUseIonicApp?: boolean;
  posAccess?: boolean;
  posSettings?: POSSettings;
  isDeleted?: boolean;
}

// User authentication data
export interface UserAuth {
  userId: UserId;
  email: string;
  userType: UserType;
  companyId?: CompanyId;
  role?: SystemRole | ObjectId;
  permissions: string[];
  locationIds: LocationId[];
  canUseIonicApp: boolean;
  posAccess: boolean;
}

// JWT Token payload for user authentication
export interface UserTokenPayload {
  id: UserId;
  userId?: UserId; // Support both formats
  email: string;
  userType: UserType;
  role?: SystemRole | ObjectId;
  companyId?: CompanyId;
  locationId?: LocationId;
  permissions?: string[];
  iat?: number;
  exp?: number;
}

// User profile (public-facing user info)
export interface UserProfile {
  _id: UserId;
  email: string;
  name?: string;
  displayName?: string;
  userType: UserType;
  role?: SystemRole | ObjectId;
  permissions: string[];
  locationIds: LocationId[];
  canUseIonicApp: boolean;
  posAccess: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// User with populated company and location data
export interface UserWithRelations extends User {
  company?: {
    _id: CompanyId;
    name: string;
  };
  locations?: Array<{
    _id: LocationId;
    name: string;
    locationType: string;
  }>;
  customRole?: {
    _id: RoleId;
    name: string;
    permissions: string[];
  };
}

// User sync data for Ionic app
export interface UserSyncData {
  users: User[];
  lastSyncTimestamp: Date;
  totalCount: number;
  hasMore: boolean;
}

// PIN authentication for POS
export interface PINAuthRequest {
  pin: string;
  companyId: CompanyId;
  locationId?: LocationId;
}

export interface PINAuthResponse {
  success: boolean;
  user?: UserProfile;
  token?: string;
  message?: string;
}

// Permission system types
export interface Permission {
  name: string;
  description: string;
  category: string;
}

export const PERMISSION_CATEGORIES = {
  INVENTORY: 'inventory',
  ORDERS: 'orders',
  USERS: 'users',
  REPORTS: 'reports',
  SETTINGS: 'settings',
  POS: 'pos'
} as const;

export const STANDARD_PERMISSIONS = {
  // Inventory permissions
  'inventory:read': 'View inventory items and levels',
  'inventory:write': 'Create and update inventory items',
  'inventory:adjust': 'Make inventory adjustments',
  'inventory:wastage': 'Record inventory wastage',
  'inventory:count': 'Perform inventory counts',
  'inventory:transfer': 'Create and manage inventory transfers',
  
  // Order permissions
  'orders:read': 'View orders',
  'orders:write': 'Create and update orders',
  'orders:approve': 'Approve orders',
  'orders:deliver': 'Mark orders as delivered',
  'orders:cancel': 'Cancel orders',
  
  // User permissions
  'users:read': 'View users',
  'users:write': 'Create and update users',
  'users:delete': 'Delete users',
  'users:permissions': 'Manage user permissions',
  
  // Settings permissions
  'settings:read': 'View settings',
  'settings:write': 'Update settings',
  'settings:company': 'Manage company settings',
  
  // POS permissions
  'pos:access': 'Access POS system',
  'pos:sales': 'Process sales',
  'pos:refunds': 'Process refunds',
  'pos:discounts': 'Apply discounts',
  'pos:reports': 'View POS reports'
} as const;

// Zod schemas for validation
export const CreateUserSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  name: z.string().optional(),
  displayName: z.string().optional(),
  userType: z.enum(['superuser', 'company_user']),
  companyId: z.string().optional(),
  role: z.string().optional(),
  permissions: z.array(z.string()).optional().default([]),
  locationIds: z.array(z.string()).optional().default([]),
  locationId: z.string().optional(),
  canUseIonicApp: z.boolean().optional().default(false),
  posAccess: z.boolean().optional().default(false),
  posSettings: z.record(z.any()).optional()
});

export const UpdateUserSchema = z.object({
  name: z.string().optional(),
  displayName: z.string().optional(),
  role: z.string().optional(),
  permissions: z.array(z.string()).optional(),
  locationIds: z.array(z.string()).optional(),
  locationId: z.string().optional(),
  canUseIonicApp: z.boolean().optional(),
  posAccess: z.boolean().optional(),
  posSettings: z.record(z.any()).optional(),
  isDeleted: z.boolean().optional()
});

export const PINAuthSchema = z.object({
  pin: z.string().length(4, 'PIN must be exactly 4 digits').regex(/^\d{4}$/, 'PIN must contain only digits'),
  companyId: z.string(),
  locationId: z.string().optional()
});

// Type guards
export const isSystemRole = (role: string): role is SystemRole => {
  return ['owner', 'admin', 'manager', 'user', 'storekeeper'].includes(role);
};

export const isCustomRole = (role: string): boolean => {
  return !isSystemRole(role) && role.length === 24; // MongoDB ObjectId length
};

// Helper functions
export const hasPermission = (user: UserAuth | User, permission: string): boolean => {
  return user.permissions?.includes(permission) ?? false;
};

export const hasAnyPermission = (user: UserAuth | User, permissions: string[]): boolean => {
  return permissions.some(permission => hasPermission(user, permission));
};

export const hasAllPermissions = (user: UserAuth | User, permissions: string[]): boolean => {
  return permissions.every(permission => hasPermission(user, permission));
};

export const canAccessLocation = (user: User, locationId: LocationId): boolean => {
  return user.locationIds.includes(locationId) || (user.locationsAccess?.includes(locationId) ?? false);
};