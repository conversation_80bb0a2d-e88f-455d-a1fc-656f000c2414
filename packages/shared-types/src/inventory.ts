// Inventory types
export interface InventoryItem {
  id: string;
  name: string;
  description?: string;
  sku: string;
  quantity: number;
  unit: string;
  cost: number;
  supplier?: string;
  companyId: string;
  locationId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface InventoryMovement {
  id: string;
  itemId: string;
  type: 'in' | 'out' | 'transfer';
  quantity: number;
  fromLocationId?: string;
  toLocationId?: string;
  reason: string;
  userId: string;
  createdAt: Date;
}