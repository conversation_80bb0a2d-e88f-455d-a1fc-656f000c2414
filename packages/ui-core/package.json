{"name": "@foodprepai/ui-core", "version": "0.1.0", "main": "dist/index.js", "types": "dist/index.d.ts", "license": "MIT", "files": ["dist"], "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "typecheck": "tsc --noEmit", "test": "jest"}, "dependencies": {"@foodprepai/shared-types": "*", "@foodprepai/design-tokens": "*", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1"}, "devDependencies": {"@foodprepai/tsconfig": "*", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/jest": "^29.5.14", "jest": "^29.7.0", "typescript": "^5.0.0"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./types": {"types": "./dist/types/index.d.ts", "default": "./dist/types/index.js"}, "./utils": {"types": "./dist/utils/index.d.ts", "default": "./dist/utils/index.js"}, "./platform": {"types": "./dist/platform/index.d.ts", "default": "./dist/platform/index.js"}}}