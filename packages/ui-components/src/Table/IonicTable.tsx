/**
 * Ionic-specific Table implementation using IonList and IonItem
 * Optimized for touch interaction on mobile devices
 */

import React from 'react';
import { TableProps, TableColumn } from '@foodprepai/ui-core/types';
import { cn } from '@foodprepai/ui-core/utils';

// Mock Ionic components for demonstration
const IonList = React.forwardRef<HTMLIonListElement, any>(({ children, ...props }, ref) => (
  <ion-list {...props} ref={ref}>
    {children}
  </ion-list>
));

const IonItem = React.forwardRef<HTMLIonItemElement, any>(({ children, button, ...props }, ref) => (
  <ion-item {...props} button={button} ref={ref}>
    {children}
  </ion-item>
));

const IonLabel = React.forwardRef<HTMLIonLabelElement, any>(({ children, ...props }, ref) => (
  <ion-label {...props} ref={ref}>
    {children}
  </ion-label>
));

const IonSkeletonText = ({ animated, style }: { animated?: boolean; style?: React.CSSProperties }) => (
  <ion-skeleton-text animated={animated} style={style} />
);

const IonCheckbox = React.forwardRef<HTMLIonCheckboxElement, any>(({ ...props }, ref) => (
  <ion-checkbox {...props} ref={ref} />
));

const IonIcon = ({ icon, slot }: { icon: string; slot?: string }) => (
  <ion-icon icon={icon} slot={slot} />
);

// Type definitions for Ionic components
interface HTMLIonListElement extends HTMLElement {}
interface HTMLIonItemElement extends HTMLElement {}
interface HTMLIonLabelElement extends HTMLElement {}
interface HTMLIonCheckboxElement extends HTMLElement {}

// Loading skeleton for Ionic
const IonicTableSkeleton: React.FC<{ columns: TableColumn[]; rows: number }> = ({ columns, rows }) => (
  <IonList>
    {/* Header skeleton */}
    <IonItem>
      {columns.map((column, index) => (
        <IonLabel key={`skeleton-header-${index}`} style={{ width: column.width }}>
          <IonSkeletonText animated style={{ width: '70%' }} />
        </IonLabel>
      ))}
    </IonItem>
    
    {/* Row skeletons */}
    {Array.from({ length: rows }).map((_, index) => (
      <IonItem key={`skeleton-row-${index}`}>
        {columns.map((column, colIndex) => (
          <IonLabel key={`skeleton-col-${colIndex}`} style={{ width: column.width }}>
            <IonSkeletonText animated style={{ width: '90%' }} />
          </IonLabel>
        ))}
      </IonItem>
    ))}
  </IonList>
);

// Mobile-optimized card layout for each row
const IonicTableRow: React.FC<{
  item: any;
  index: number;
  columns: TableColumn[];
  onRowClick?: (item: any, index: number) => void;
  selectable?: boolean;
  isSelected?: boolean;
  onSelectionChange?: (checked: boolean) => void;
}> = ({ 
  item, 
  index, 
  columns, 
  onRowClick, 
  selectable, 
  isSelected, 
  onSelectionChange 
}) => {
  const handleClick = () => {
    if (onRowClick) {
      onRowClick(item, index);
    }
  };

  const handleSelectionChange = (event: any) => {
    event.stopPropagation();
    if (onSelectionChange) {
      onSelectionChange(event.detail.checked);
    }
  };

  // For mobile, we show data in a more compact, card-like format
  const primaryColumn = columns[0];
  const secondaryColumns = columns.slice(1, 3); // Show up to 2 additional columns
  const remainingColumns = columns.slice(3);

  const primaryValue = primaryColumn.accessor 
    ? primaryColumn.accessor(item) 
    : item[primaryColumn.key];
  
  const primaryDisplay = primaryColumn.render 
    ? primaryColumn.render(primaryValue, item, index)
    : String(primaryValue);

  return (
    <IonItem button={!!onRowClick} onClick={handleClick}>
      {selectable && (
        <IonCheckbox
          slot="start"
          checked={isSelected}
          onIonChange={handleSelectionChange}
          aria-label={`Select row ${index + 1}`}
        />
      )}
      
      <IonLabel>
        {/* Primary content */}
        <h2 className="font-medium text-gray-900">{primaryDisplay}</h2>
        
        {/* Secondary content */}
        {secondaryColumns.length > 0 && (
          <div className="mt-1 space-y-1">
            {secondaryColumns.map((column) => {
              const value = column.accessor ? column.accessor(item) : item[column.key];
              const display = column.render ? column.render(value, item, index) : String(value);
              
              return (
                <p key={column.key} className="text-sm text-gray-600">
                  <span className="font-medium">{column.header}:</span> {display}
                </p>
              );
            })}
          </div>
        )}
        
        {/* Remaining columns as compact info */}
        {remainingColumns.length > 0 && (
          <div className="mt-2 flex flex-wrap gap-2">
            {remainingColumns.map((column) => {
              const value = column.accessor ? column.accessor(item) : item[column.key];
              const display = column.render ? column.render(value, item, index) : String(value);
              
              return (
                <span key={column.key} className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800">
                  {column.header}: {display}
                </span>
              );
            })}
          </div>
        )}
      </IonLabel>
      
      {onRowClick && (
        <IonIcon icon="chevron-forward" slot="end" />
      )}
    </IonItem>
  );
};

export const IonicTable = <T extends Record<string, any>>({
  columns,
  data,
  loading = false,
  loadingRows = 3,
  emptyMessage = 'No data available',
  onRowClick,
  selectable = false,
  selectedRows = [],
  onSelectionChange,
  sortBy,
  sortDirection,
  onSort,
  className,
  testId,
  ...props
}: TableProps<T>) => {
  const handleRowSelect = (item: T, index: number, checked: boolean) => {
    if (!onSelectionChange) return;
    
    const itemId = item.id || index;
    const newSelection = checked
      ? [...selectedRows, itemId]
      : selectedRows.filter(id => id !== itemId);
    
    onSelectionChange(newSelection);
  };

  const handleSelectAll = (checked: boolean) => {
    if (!onSelectionChange) return;
    
    if (checked) {
      const allIds = data.map((item, index) => item.id || index);
      onSelectionChange(allIds);
    } else {
      onSelectionChange([]);
    }
  };

  const handleSort = (column: TableColumn<T>) => {
    if (!column.sortable || !onSort) return;
    
    const newDirection = sortBy === column.key 
      ? (sortDirection === 'asc' ? 'desc' : 'asc')
      : 'asc';
    
    onSort(column.key, newDirection);
  };

  if (loading) {
    return (
      <div className={cn("ionic-table", className)} data-testid={testId} {...props}>
        <IonicTableSkeleton columns={columns} rows={loadingRows} />
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className={cn("ionic-table", className)} data-testid={testId} {...props}>
        <IonList>
          <IonItem>
            <IonLabel className="ion-text-center py-8 text-gray-500">
              {emptyMessage}
            </IonLabel>
          </IonItem>
        </IonList>
      </div>
    );
  }

  const isAllSelected = data.length > 0 && selectedRows.length === data.length;
  const hasSelection = selectedRows.length > 0;

  return (
    <div className={cn("ionic-table", className)} data-testid={testId} {...props}>
      <IonList>
        {/* Header with sort options (mobile-optimized) */}
        <IonItem className="sticky top-0 bg-gray-50 border-b">
          {selectable && (
            <IonCheckbox
              slot="start"
              checked={isAllSelected}
              indeterminate={hasSelection && !isAllSelected}
              onIonChange={(e) => handleSelectAll(e.detail.checked)}
              aria-label="Select all rows"
            />
          )}
          
          <IonLabel>
            <div className="flex items-center justify-between">
              <span className="font-medium text-gray-900">
                {data.length} {data.length === 1 ? 'item' : 'items'}
              </span>
              
              {/* Sort controls for mobile */}
              {columns.some(col => col.sortable) && (
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-500">Sort by:</span>
                  <select
                    className="text-sm border rounded px-2 py-1"
                    value={sortBy || ''}
                    onChange={(e) => {
                      const column = columns.find(col => col.key === e.target.value);
                      if (column) handleSort(column);
                    }}
                  >
                    <option value="">None</option>
                    {columns.filter(col => col.sortable).map(column => (
                      <option key={column.key} value={column.key}>
                        {column.header} {sortBy === column.key ? (sortDirection === 'asc' ? '↑' : '↓') : ''}
                      </option>
                    ))}
                  </select>
                </div>
              )}
            </div>
          </IonLabel>
        </IonItem>

        {/* Data rows */}
        {data.map((item, index) => {
          const isSelected = selectedRows.includes(item.id || index);
          
          return (
            <IonicTableRow
              key={item.id || index}
              item={item}
              index={index}
              columns={columns}
              onRowClick={onRowClick}
              selectable={selectable}
              isSelected={isSelected}
              onSelectionChange={(checked) => handleRowSelect(item, index, checked)}
            />
          );
        })}
      </IonList>
    </div>
  );
};

IonicTable.displayName = 'IonicTable';